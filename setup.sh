#!/bin/bash

# Nine Trade Maker 环境配置脚本
# 自动创建虚拟环境、安装依赖和配置环境文件

set -e

VENV_NAME="nine-trade-env"
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python版本
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，请先安装Python 3.8或更高版本"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    required_version="3.8"
    
    if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" = "$required_version" ]; then
        print_status "Python版本检查通过: $python_version"
    else
        print_error "Python版本过低，需要3.8或更高版本，当前版本: $python_version"
        exit 1
    fi
}

# 检查项目目录
check_project_dir() {
    if [ ! -f "requirements.txt" ]; then
        print_error "当前目录不是Nine Trade Maker项目根目录（缺少requirements.txt）"
        exit 1
    fi
}

# 创建虚拟环境
create_venv() {
    if [ -d "$VENV_NAME" ]; then
        print_warning "虚拟环境 $VENV_NAME 已存在"
        read -p "是否要删除并重新创建？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "删除现有虚拟环境..."
            # 安全检查：确保删除的是虚拟环境目录
            if [ -f "$VENV_NAME/pyvenv.cfg" ] || [ -f "$VENV_NAME/bin/activate" ]; then
                rm -rf "$VENV_NAME"
            else
                print_error "$VENV_NAME 不是有效的虚拟环境目录"
                exit 1
            fi
        else
            print_status "使用现有虚拟环境"
            return
        fi
    fi
    
    print_status "创建虚拟环境: $VENV_NAME"
    python3 -m venv "$VENV_NAME"
}

# 激活虚拟环境
activate_venv() {
    if [ ! -f "$VENV_NAME/bin/activate" ]; then
        print_error "虚拟环境激活脚本不存在: $VENV_NAME/bin/activate"
        print_error "虚拟环境可能损坏，请删除后重新创建"
        exit 1
    fi
    
    print_status "激活虚拟环境"
    source "$VENV_NAME/bin/activate"
    
    # 升级pip
    print_status "升级pip"
    pip install --upgrade pip
}

# 安装依赖
install_dependencies() {
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    print_status "安装项目依赖"
    pip install -r requirements.txt
}

# 配置环境文件
setup_env_file() {
    if [ ! -f ".env" ]; then
        if [ -f "env.example" ]; then
            print_status "复制环境配置文件"
            cp env.example .env
            print_warning "请编辑 .env 文件，配置API密钥等信息"
        else
            print_warning "env.example 文件不存在，请手动创建 .env 文件"
        fi
    else
        print_status ".env 文件已存在，跳过复制"
    fi
}

# 显示完成信息
show_completion_info() {
    echo
    print_status "=========================================="
    print_status "Nine Trade Maker 环境配置完成！"
    print_status "=========================================="
    echo
    echo "下一步操作："
    echo "1. 激活虚拟环境:"
    echo -e "   ${YELLOW}source $VENV_NAME/bin/activate${NC}"
    echo
    echo "2. 配置环境变量:"
    echo -e "   ${YELLOW}编辑 .env 文件，填入API密钥${NC}"
    echo
    echo "3. 启动交易机器人:"
    echo -e "   ${YELLOW}flask start-trading-bot --strategy [策略名]${NC}"
    echo
    echo "可用策略："
    echo "  - cex_price_balance (CEX价格平衡策略)"
    echo "  - liquidity_provider (流动性提供策略)"
    echo "  - mirror_binance (币安镜像策略)"
    echo "  - 等等..."
    echo
}

# 主流程
main() {
    print_status "开始配置 Nine Trade Maker 环境"
    echo
    
    check_project_dir
    check_python
    create_venv
    activate_venv
    install_dependencies
    setup_env_file
    show_completion_info
}

# 错误处理
trap 'print_error "配置过程中发生错误，请检查错误信息"; exit 1' ERR

# 运行主流程
main