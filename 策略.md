# 价格同步策略
我们需要实现一个基于 nine clinet 的价格同步策略，该策略可以把 raydium或者 cex 上的价格同步到 nine cex 上。

我们已经有了 nince cline 和 makert_maker ， 获取 cex 和 raydium 等一系列基础服务，位于 /Users/<USER>/Desktop/Nine/nine-trade-maker/app/services。

# 策略逻辑
我们需要为 nine cex 提供指定币种的流动性和价格同步，避免价差，因此策略需要：
- 同步指定币种的价格，策略配置文件支持选择 cex 或者 raydium 价格数据源，raydium 要支持币种合约地址
- 提供流动性，策略配置文件支持选择流动性深度层数，每一层的价差，每一层的流动性数量（要区间随机）
- 当价格发生变动时，我们需要自成交吃单，来完成价格的同步行为
- 同步完成后，继续提供流动性   

# 策略配置
- 价格同步目标交易对
- 价格同步数据源
- 价格同步目标价差
- 价格同步目标流动性深度
- 价格同步目标流动性数量

# 实现
为我建立实现的计划文档，并逐步更新 