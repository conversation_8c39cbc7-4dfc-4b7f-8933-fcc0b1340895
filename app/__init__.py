import os
from flask import Flask, jsonify
from dotenv import load_dotenv
from app.services.nine_client import NineClient
from app.services.binance_client import BinanceClient
from app.services.market_maker_service import TradingBotService
from app.strategies import MirrorBinanceStrategy, VolumeKlineStrategy, BaseStrategy, CumulativeDepthStrategy, LiquidityProviderStrategy, RaydiumPriceBalanceStrategy, CexPriceBalanceStrategy, MirrorPriceStrategy, GradientLiquidityStrategy, AdaptiveKlineStrategy, ActiveStrategy
# Updated import for CLI commands
from app.services.market_maker_service import (
    start_trading_bot_command, 
    stop_trading_bot_command, 
    run_bot_once_command,
    init_liquidity_baseline_command,
    clean_strategy_orders_command
)
import logging
from flask_cors import CORS
from typing import Optional, Dict, Any

def create_app(test_config=None):
    """应用工厂函数"""
    load_dotenv() # 加载 .env 文件中的环境变量

    app = Flask(__name__, instance_relative_config=True)
    
    app.config.from_mapping(
        SECRET_KEY=os.environ.get('SECRET_KEY', 'dev'),
        NINE_API_URL=os.environ.get('NINE_API_URL'),
        NINE_WALLET_API_URL=os.environ.get('NINE_WALLET_API_URL'),
        BINANCE_API_URL=os.environ.get('BINANCE_API_URL', 'https://api.binance.com'),
        BINANCE_API_KEY=os.environ.get('BINANCE_API_KEY'),
        BINANCE_API_SECRET=os.environ.get('BINANCE_API_SECRET'),

        # --- Binance Testnet --
        BINANCE_TESTNET_API_URL=os.environ.get('BINANCE_TESTNET_API_URL', 'https://testnet.binance.vision'),
        BINANCE_TESTNET_API_KEY=os.environ.get('BINANCE_TESTNET_API_KEY'),
        BINANCE_TESTNET_API_SECRET=os.environ.get('BINANCE_TESTNET_API_SECRET'),
        USE_BINANCE_TESTNET=os.environ.get('USE_BINANCE_TESTNET', 'False').lower() in ('true', '1', 't', 'yes'),

        # --- 通用 Nine CEX 订单参数 ---
        NINE_ACCOUNT_TYPE=os.environ.get('NINE_ACCOUNT_TYPE', '1'),
        NINE_ORDER_TYPE_LIMIT=os.environ.get('NINE_ORDER_TYPE_LIMIT', '1'),
        NINE_ORDER_DIR_BUY=os.environ.get('NINE_ORDER_DIR_BUY', '1'),
        NINE_ORDER_DIR_SELL=os.environ.get('NINE_ORDER_DIR_SELL', '2'),

        # --- Market Maker (MirrorBinanceStrategy) 特定配置 ---
        MM_BINANCE_SYMBOL=os.environ.get('MM_BINANCE_SYMBOL', 'BTCUSDT'),
        MM_NINE_CEX_SYMBOL=os.environ.get('MM_NINE_CEX_SYMBOL'),
        MM_SPREAD_PERCENTAGE=os.environ.get('MM_SPREAD_PERCENTAGE'),
        MM_NINE_API_KEY=os.environ.get('MM_NINE_API_KEY'), # Dedicated for MirrorBinanceStrategy
        MM_NINE_API_SECRET=os.environ.get('MM_NINE_API_SECRET'), # Dedicated for MirrorBinanceStrategy
        MM_UPDATE_INTERVAL=os.environ.get('MM_UPDATE_INTERVAL', '60'),
        MM_PRICE_PRECISION=os.environ.get('MM_PRICE_PRECISION'),
        MM_QTY_PRECISION=os.environ.get('MM_QTY_PRECISION'),
        MM_BINANCE_DEPTH_LEVELS=os.environ.get('MM_BINANCE_DEPTH_LEVELS'),
        MM_QUANTITY_COEFFICIENT=os.environ.get('MM_QUANTITY_COEFFICIENT', '1.0'),
        
        TRADING_STRATEGY=os.environ.get('TRADING_STRATEGY'),
        ENABLE_BINANCE_CLIENT=os.environ.get('ENABLE_BINANCE_CLIENT', 'False').lower() in ('true', '1', 't', 'yes'),

        # --- VolumeKlineStrategy 配置加载 ---
        VK_TRADING_PAIR=os.environ.get('VK_TRADING_PAIR'),
        VK_INTERVAL_SECONDS=os.environ.get('VK_INTERVAL_SECONDS', '10'),
        VK_ORDER_AMOUNT=os.environ.get('VK_ORDER_AMOUNT'),
        VK_MIN_TRADE_QTY_ASSET=os.environ.get('VK_MIN_TRADE_QTY_ASSET'),
        VK_PRICE_PRECISION=os.environ.get('VK_PRICE_PRECISION'),
        VK_QTY_PRECISION=os.environ.get('VK_QTY_PRECISION'),
        VK_ENABLE_PERIODIC_TRADE_CHECK=os.environ.get('VK_ENABLE_PERIODIC_TRADE_CHECK', 'true'),
        VK_NINE_API_KEY=os.environ.get('VK_NINE_API_KEY'), # Dedicated for VolumeKlineStrategy
        VK_NINE_API_SECRET=os.environ.get('VK_NINE_API_SECRET'), # Dedicated for VolumeKlineStrategy
        VK_NINE_POOL_PRECISION_API_PARAM=os.environ.get('VK_NINE_POOL_PRECISION_API_PARAM', '0.01'), # Nine CEX 盘口聚合精度

        # --- CumulativeDepthStrategy 配置加载 ---
        CDS_TRADING_PAIR=os.environ.get('CDS_TRADING_PAIR'),
        CDS_INTERVAL_SECONDS=os.environ.get('CDS_INTERVAL_SECONDS', '60'),
        CDS_ORDER_AMOUNT_BASE=os.environ.get('CDS_ORDER_AMOUNT_BASE'), 
        CDS_CUMULATIVE_BUY_DEPTH_AMOUNT=os.environ.get('CDS_CUMULATIVE_BUY_DEPTH_AMOUNT'),
        CDS_CUMULATIVE_SELL_DEPTH_AMOUNT=os.environ.get('CDS_CUMULATIVE_SELL_DEPTH_AMOUNT'),
        CDS_PRICE_ADJUSTMENT_BUY=os.environ.get('CDS_PRICE_ADJUSTMENT_BUY'),
        CDS_PRICE_ADJUSTMENT_SELL=os.environ.get('CDS_PRICE_ADJUSTMENT_SELL'),
        CDS_MIN_SPREAD_PRICE=os.environ.get('CDS_MIN_SPREAD_PRICE'),
        CDS_SPREAD_ADJUSTMENT_BUY=os.environ.get('CDS_SPREAD_ADJUSTMENT_BUY'),
        CDS_SPREAD_ADJUSTMENT_SELL=os.environ.get('CDS_SPREAD_ADJUSTMENT_SELL'),
        CDS_MIN_ORDER_QTY_BASE=os.environ.get('CDS_MIN_ORDER_QTY_BASE'),
        CDS_PRICE_PRECISION=os.environ.get('CDS_PRICE_PRECISION'),
        CDS_QTY_PRECISION=os.environ.get('CDS_QTY_PRECISION'),
        CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN=os.environ.get('CDS_MAX_POOL_DEPTH_LEVELS_TO_SCAN', '20'),
        CDS_NINE_API_KEY=os.environ.get('CDS_NINE_API_KEY'), # Dedicated for CumulativeDepthStrategy
        CDS_NINE_API_SECRET=os.environ.get('CDS_NINE_API_SECRET'), # Dedicated for CumulativeDepthStrategy

        # --- CrossExchangeArbitrageStrategy 配置加载 ---
        ARB_NINE_SYMBOL=os.environ.get('ARB_NINE_SYMBOL'),
        ARB_NINE_API_KEY=os.environ.get('ARB_NINE_API_KEY'),
        ARB_NINE_API_SECRET=os.environ.get('ARB_NINE_API_SECRET'),
        ARB_NINE_FEE_RATE=os.environ.get('ARB_NINE_FEE_RATE', '0.001'), # 0.1%
        ARB_BINANCE_SYMBOL=os.environ.get('ARB_BINANCE_SYMBOL'),
        # ARB_BINANCE_API_KEY: Will use the general BINANCE_API_KEY or BINANCE_TESTNET_API_KEY
        # ARB_BINANCE_API_SECRET: Will use the general BINANCE_API_SECRET or BINANCE_TESTNET_API_SECRET
        ARB_BINANCE_FEE_RATE=os.environ.get('ARB_BINANCE_FEE_RATE', '0.001'), # 0.1%
        ARB_MIN_PROFIT_PCT=os.environ.get('ARB_MIN_PROFIT_PCT', '0.002'), # 0.2%
        ARB_ORDER_AMOUNT_MODE=os.environ.get('ARB_ORDER_AMOUNT_MODE', 'fixed'),
        ARB_ORDER_AMOUNT_BASE=os.environ.get('ARB_ORDER_AMOUNT_BASE', '0.001'),
        ARB_ORDER_AMOUNT_PERCENTAGE=os.environ.get('ARB_ORDER_AMOUNT_PERCENTAGE', '0.1'),
        ARB_ORDER_AMOUNT_MAX=os.environ.get('ARB_ORDER_AMOUNT_MAX', '0.01'),
        ARB_ORDER_AMOUNT_MIN=os.environ.get('ARB_ORDER_AMOUNT_MIN', '0.0001'),
        ARB_SLIPPAGE_FACTOR=os.environ.get('ARB_SLIPPAGE_FACTOR', '0.0005'), # 滑点保护因子, e.g., 0.0005 for 0.05%
        ARB_MAX_PENDING_ORDERS=os.environ.get('ARB_MAX_PENDING_ORDERS', '5'),
        ARB_ORDER_TIMEOUT_SECONDS=os.environ.get('ARB_ORDER_TIMEOUT_SECONDS', '300'), # 套利订单超时撤单时间 (秒)
        ARB_BINANCE_IOC_CHECK_TIMEOUT=os.environ.get('ARB_BINANCE_IOC_CHECK_TIMEOUT', '5'),
        ARB_BINANCE_PRICE_PRECISION=os.environ.get('ARB_BINANCE_PRICE_PRECISION', '8'),
        ARB_BINANCE_QTY_PRECISION=os.environ.get('ARB_BINANCE_QTY_PRECISION', '6'),
        ARB_BINANCE_MIN_BASE_QTY=os.environ.get('ARB_BINANCE_MIN_BASE_QTY', '0.0001'),
        ARB_NINE_POOL_PRECISION_API_PARAM=os.environ.get('ARB_NINE_POOL_PRECISION_API_PARAM', '0.01'),

        # --- LiquidityProviderStrategy 配置加载 ---
        LP_NINE_API_KEY=os.environ.get('LP_NINE_API_KEY'),
        LP_NINE_API_SECRET=os.environ.get('LP_NINE_API_SECRET'),
        LP_INITIAL_USDT=os.environ.get('LP_INITIAL_USDT', '3000'),
        LP_COIN_SELL_PERCENTAGE=os.environ.get('LP_COIN_SELL_PERCENTAGE', '0.15'),
        LP_SPREAD_PERCENTAGE=os.environ.get('LP_SPREAD_PERCENTAGE', '0.002'),
        LP_ORDER_SIZE_PERCENTAGE=os.environ.get('LP_ORDER_SIZE_PERCENTAGE', '0.1'),
        LP_MIN_ORDER_AMOUNT=os.environ.get('LP_MIN_ORDER_AMOUNT', '10'),
        LP_SELL_FREQUENCY=os.environ.get('LP_SELL_FREQUENCY', '300'),
        LP_SELL_AMOUNT_PERCENTAGE=os.environ.get('LP_SELL_AMOUNT_PERCENTAGE', '0.01'),
        LP_PRICE_PRECISION=os.environ.get('LP_PRICE_PRECISION', '2'),
        LP_QTY_PRECISION=os.environ.get('LP_QTY_PRECISION', '5'),
        LP_UPDATE_INTERVAL=os.environ.get('LP_UPDATE_INTERVAL', '60'),
        
        # EnhancedLiquidityProviderStrategy 配置
        ELP_NINE_API_KEY=os.environ.get('ELP_NINE_API_KEY'),
        ELP_NINE_API_SECRET=os.environ.get('ELP_NINE_API_SECRET'),
        ELP_OPERATING_MODE=os.environ.get('ELP_OPERATING_MODE', 'manual'),
        ELP_MANUAL_PAIRS=os.environ.get('ELP_MANUAL_PAIRS', ''),
        ELP_TOTAL_USDT=os.environ.get('ELP_TOTAL_USDT', '3000'),
        ELP_BASE_PRICE=os.environ.get('ELP_BASE_PRICE', '0.0000161'),
        ELP_PRICE_MULTIPLIERS=os.environ.get('ELP_PRICE_MULTIPLIERS', '0.5,1.0,2.0'),
        ELP_ORDERS_PER_LEVEL=os.environ.get('ELP_ORDERS_PER_LEVEL', '3'),
        ELP_SELL_FREQUENCY=os.environ.get('ELP_SELL_FREQUENCY', '300'),
        ELP_SELL_PERCENTAGE=os.environ.get('ELP_SELL_PERCENTAGE', '0.01'),
        ELP_MIN_SELL_AMOUNT=os.environ.get('ELP_MIN_SELL_AMOUNT', '100'),
        ELP_PRICE_MODE=os.environ.get('ELP_PRICE_MODE', 'fixed'),
        ELP_ENABLE_PUMP_DUMP=os.environ.get('ELP_ENABLE_PUMP_DUMP', 'false'),
        ELP_PUMP_TARGET_RATIO=os.environ.get('ELP_PUMP_TARGET_RATIO', '2.0'),
        ELP_PUMP_TRIGGER_PRICE=os.environ.get('ELP_PUMP_TRIGGER_PRICE', '0.0000161'),
        ELP_PRICE_PRECISION=os.environ.get('ELP_PRICE_PRECISION', '8'),
        ELP_QTY_PRECISION=os.environ.get('ELP_QTY_PRECISION', '6'),
        ELP_UPDATE_INTERVAL=os.environ.get('ELP_UPDATE_INTERVAL', '60'),
        ELP_ENABLE_PERIODIC_TRADE_CHECK=os.environ.get('ELP_ENABLE_PERIODIC_TRADE_CHECK', 'false'),
        
        # RaydiumPriceBalanceStrategy 配置
        RPB_NINE_API_KEY=os.environ.get('RPB_NINE_API_KEY'),
        RPB_NINE_API_SECRET=os.environ.get('RPB_NINE_API_SECRET'),
        RPB_TRADING_PAIR=os.environ.get('RPB_TRADING_PAIR', 'BTC/USDT'),
        RPB_CONTRACT_ADDRESS=os.environ.get('RPB_CONTRACT_ADDRESS', ''),
        RPB_PRICE_TOLERANCE=os.environ.get('RPB_PRICE_TOLERANCE', '0.002'),
        RPB_AGGRESSIVE_THRESHOLD=os.environ.get('RPB_AGGRESSIVE_THRESHOLD', '0.01'),
        RPB_BASE_ORDER_AMOUNT=os.environ.get('RPB_BASE_ORDER_AMOUNT', '100'),
        RPB_MAX_ORDER_AMOUNT=os.environ.get('RPB_MAX_ORDER_AMOUNT', '1000'),
        RPB_MIN_ORDER_AMOUNT=os.environ.get('RPB_MIN_ORDER_AMOUNT', '1'),
        RPB_EAT_ORDER_MULTIPLIER=os.environ.get('RPB_EAT_ORDER_MULTIPLIER', '2'),
        RPB_LIQUIDITY_SPREAD=os.environ.get('RPB_LIQUIDITY_SPREAD', '0.001'),
        RPB_LIQUIDITY_LEVELS=os.environ.get('RPB_LIQUIDITY_LEVELS', '5'),
        RPB_LIQUIDITY_AMOUNT_PER_LEVEL=os.environ.get('RPB_LIQUIDITY_AMOUNT_PER_LEVEL', '50'),
        RPB_PRICE_PRECISION=os.environ.get('RPB_PRICE_PRECISION', '2'),
        RPB_QTY_PRECISION=os.environ.get('RPB_QTY_PRECISION', '6'),
        RPB_UPDATE_INTERVAL=os.environ.get('RPB_UPDATE_INTERVAL', '30'),
        RPB_SPREADS=os.environ.get('RPB_SPREADS', '0.001,0.002,0.005,0.01,0.02'),
        RPB_INITIAL_SPREADS=os.environ.get('RPB_INITIAL_SPREADS', '0.001,0.002,0.005,0.01,0.02'),
        RPB_LEVEL_MULTIPLIERS=os.environ.get('RPB_LEVEL_MULTIPLIERS', '1.0,1.2,1.5,2.0,3.0'),
        RPB_PUMP_STEP_FACTORS=os.environ.get('RPB_PUMP_STEP_FACTORS', '1.5,2.0,2.3,2.6,2.9'),
        RPB_PRICE_LIMIT_FACTOR=os.environ.get('RPB_PRICE_LIMIT_FACTOR', '2.9'),
        RPB_TARGET_PRICE_OFFSET=os.environ.get('RPB_TARGET_PRICE_OFFSET', '0'),
        RPB_PROGRESSIVE_PUMP_THRESHOLD=os.environ.get('RPB_PROGRESSIVE_PUMP_THRESHOLD', '0.5'),
        RPB_SMALL_TRADE_AMOUNT=os.environ.get('RPB_SMALL_TRADE_AMOUNT', '1'),
        RPB_MEDIUM_TRADE_AMOUNT=os.environ.get('RPB_MEDIUM_TRADE_AMOUNT', '3'),
        
        # MirrorPriceStrategy 配置
        MP_NINE_API_KEY=os.environ.get('MP_NINE_API_KEY'),
        MP_NINE_API_SECRET=os.environ.get('MP_NINE_API_SECRET'),
        MP_PRICE_SOURCE=os.environ.get('MP_PRICE_SOURCE', 'binance'),
        MP_SOURCE_SYMBOL=os.environ.get('MP_SOURCE_SYMBOL', 'BTCUSDT'),
        MP_TARGET_SYMBOL=os.environ.get('MP_TARGET_SYMBOL', 'BTC/USDT'),
        MP_RAYDIUM_CONTRACT=os.environ.get('MP_RAYDIUM_CONTRACT'),
        MP_PRICE_OFFSET=os.environ.get('MP_PRICE_OFFSET', '0'),
        MP_SYNC_AMOUNT=os.environ.get('MP_SYNC_AMOUNT', '10'),
        MP_UPDATE_INTERVAL=os.environ.get('MP_UPDATE_INTERVAL', '30'),
        MP_PRICE_PRECISION=os.environ.get('MP_PRICE_PRECISION', '8'),
        MP_QTY_PRECISION=os.environ.get('MP_QTY_PRECISION', '2'),
        
        # 通用Nine API配置
        NINE_API_KEY=os.environ.get('NINE_API_KEY'),
        NINE_API_SECRET=os.environ.get('NINE_API_SECRET')
    )

    if test_config is None:
        # 当不测试时，加载实例配置 (如果存在)
        app.config.from_pyfile('config.py', silent=True)
    else:
        # 加载测试配置
        app.config.from_mapping(test_config)

    # 确保实例文件夹存在
    try:
        os.makedirs(app.instance_path)
    except OSError:
        pass

    # 设置日志级别并防止重复输出
    app.logger.setLevel(logging.INFO)
    # 禁用日志传播，避免重复输出到根logger
    app.logger.propagate = False
    
    # 简化初始化日志，使用中文
    nine_api_url = app.config.get("NINE_API_URL")
    nine_wallet_api_url = app.config.get("NINE_WALLET_API_URL")
    client_init_api_key = app.config.get("NINE_API_KEY") 
    client_init_api_secret = app.config.get("NINE_API_SECRET")

    if not all([nine_api_url, client_init_api_key, client_init_api_secret]):
        app.logger.warning("Nine Cex: 配置不完整，部分功能可能受限")
        app.nine_client = None
    else:
        try:
            app.nine_client = NineClient(
                api_url=nine_api_url, 
                wallet_api_url=nine_wallet_api_url,
                api_key=client_init_api_key, 
                secret=client_init_api_secret, 
                logger=app.logger
            )
        except Exception as e:
            app.logger.error(f"Nine Cex 初始化失败: {e}")
            app.nine_client = None

    # Binance 客户端初始化
    use_testnet_config = app.config.get('USE_BINANCE_TESTNET', False)
    binance_selected_api_url: Optional[str] = None
    binance_selected_api_key: Optional[str] = None
    binance_selected_api_secret: Optional[str] = None

    if use_testnet_config:
        binance_selected_api_url = app.config.get("BINANCE_TESTNET_API_URL")
        binance_selected_api_key = app.config.get("BINANCE_TESTNET_API_KEY")
        binance_selected_api_secret = app.config.get("BINANCE_TESTNET_API_SECRET")
    else:
        binance_selected_api_url = app.config.get("BINANCE_API_URL")
        binance_selected_api_key = app.config.get("BINANCE_API_KEY")
        binance_selected_api_secret = app.config.get("BINANCE_API_SECRET")

    if not binance_selected_api_url:
        app.logger.warning("币安客户端: 未配置 API URL")
        app.binance_client = None
    else:
        try:
            app.binance_client = BinanceClient(
                base_url=binance_selected_api_url,
                api_key=binance_selected_api_key,
                api_secret=binance_selected_api_secret,
            )
            env_type = "测试网" if use_testnet_config else "主网"
            auth_status = "已认证" if binance_selected_api_key else "仅公开API"
            app.logger.info(f"币安客户端初始化成功: {env_type} ({auth_status})")
        except Exception as e:
            app.logger.error(f"币安客户端初始化失败: {e}")
            app.binance_client = None
            
    # 不在应用初始化时创建交易机器人服务
    # 只有在CLI命令执行时才创建，这样可以正确使用命令行参数指定的策略
    app.trading_bot_service = None

    # 注册蓝图
    from .api import market_routes, trade_routes
    from .api import binance_routes, inner_pool_routes
    
    app.register_blueprint(market_routes.market_bp)
    app.register_blueprint(trade_routes.trade_bp)
    app.register_blueprint(binance_routes.binance_bp)
    app.register_blueprint(inner_pool_routes.inner_pool_bp)

    # 通用错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({"code": 404, "message": "资源未找到", "data": None}), 404

    @app.errorhandler(500)
    def internal_error(error):
        # 实际错误已在各路由的 try-except 中记录
        return jsonify({"code": 500, "message": "服务器内部错误", "data": None}), 500
    
    @app.errorhandler(400)
    def bad_request_error(error):
        # Flask 自身的 400 错误，例如 JSON 解析失败
        # jsonify() 中的错误通常由我们的代码处理并返回特定的消息
        # 如果 error 对象有 description，则使用它
        message = error.description if hasattr(error, 'description') else "错误的请求"
        return jsonify({"code": 400, "message": message, "data": None}), 400

    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查端点"""
        return jsonify({"status": "healthy", "message": "Nine Trade Maker is running."}), 200

    # 注册 CLI 命令
    app.cli.add_command(start_trading_bot_command)
    app.cli.add_command(stop_trading_bot_command)
    app.cli.add_command(run_bot_once_command)
    app.cli.add_command(init_liquidity_baseline_command)
    app.cli.add_command(clean_strategy_orders_command)
    
    return app 

def setup_trading_bot(app: Flask, strategy_override: Optional[str] = None) -> Optional[TradingBotService]:
    # 策略选择
    strategy_name_to_use: Optional[str] = None
    if strategy_override:
        strategy_name_to_use = strategy_override.lower()
        app.logger.info(f"使用命令行指定策略: {strategy_name_to_use}")
    else:
        strategy_name_from_env = app.config.get("TRADING_STRATEGY")
        if strategy_name_from_env:
            strategy_name_to_use = strategy_name_from_env.lower()
            app.logger.info(f"使用环境变量策略: {strategy_name_to_use}")
        else:
            strategy_name_to_use = "mirror_binance" # Default
            app.logger.info(f"使用默认策略: {strategy_name_to_use}")

    # 加载策略特定配置
    from app.services.config_loader import load_config_for_strategy
    try:
        complete_config = load_config_for_strategy(strategy_name_to_use)
        # 合并到当前配置中
        config = {**dict(app.config), **complete_config}
        app.logger.info(f"已加载策略 {strategy_name_to_use} 的完整配置")
    except Exception as e:
        app.logger.error(f"加载策略配置失败: {e}")
        config = dict(app.config)

    # Use the globally initialized NineClient from the app context
    nine_client_instance = app.nine_client 
    if not nine_client_instance:
        app.logger.error("Nine客户端不可用")
        return None

    binance_client_instance = app.binance_client # This might be None if not enabled/failed
    binance_client_enabled = config.get("ENABLE_BINANCE_CLIENT", False)

    strategy_instance: Optional[BaseStrategy] = None

    if strategy_name_to_use == "mirror_binance":
        if not binance_client_enabled or not binance_client_instance:
            app.logger.error("镜像币安策略需要币安客户端")
            return None
        strategy_instance = MirrorBinanceStrategy(
            config=dict(config),
            nine_client=nine_client_instance, # Use shared NineClient
            binance_client=binance_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("MM_NINE_API_KEY")
        strategy_instance.api_secret = config.get("MM_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "volume_kline":
        strategy_instance = VolumeKlineStrategy(
            config=dict(config),
            nine_client=nine_client_instance, # Use shared NineClient
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器 - 优先使用通用密钥
        strategy_instance.api_key = config.get("NINE_API_KEY") or config.get("VK_NINE_API_KEY")
        strategy_instance.api_secret = config.get("NINE_API_SECRET") or config.get("VK_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "cumulative_depth":
        strategy_instance = CumulativeDepthStrategy(
            config=dict(config),
            nine_client=nine_client_instance, # Use shared NineClient
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("CDS_NINE_API_KEY")
        strategy_instance.api_secret = config.get("CDS_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "cross_exchange_arbitrage":
        if not binance_client_enabled or not binance_client_instance:
            app.logger.error("跨交易所套利策略需要币安客户端")
            return None
        from app.strategies.cross_exchange_arbitrage_strategy import CrossExchangeArbitrageStrategy
        strategy_instance = CrossExchangeArbitrageStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            binance_client=binance_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("ARB_NINE_API_KEY")
        strategy_instance.api_secret = config.get("ARB_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "liquidity_provider":
        strategy_instance = LiquidityProviderStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("LP_NINE_API_KEY")
        strategy_instance.api_secret = config.get("LP_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "enhanced_liquidity_provider":
        from app.strategies.enhanced_liquidity_provider_strategy import EnhancedLiquidityProviderStrategy
        strategy_instance = EnhancedLiquidityProviderStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("ELP_NINE_API_KEY")
        strategy_instance.api_secret = config.get("ELP_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "raydium_price_balance":
        strategy_instance = RaydiumPriceBalanceStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器 - 优先使用通用密钥
        strategy_instance.api_key = config.get("NINE_API_KEY") or config.get("RPB_NINE_API_KEY")
        strategy_instance.api_secret = config.get("NINE_API_SECRET") or config.get("RPB_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "cex_price_balance":
        strategy_instance = CexPriceBalanceStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器 - 优先使用通用密钥
        strategy_instance.api_key = config.get("NINE_API_KEY") or config.get("CPB_NINE_API_KEY")
        strategy_instance.api_secret = config.get("NINE_API_SECRET") or config.get("CPB_NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
        
    elif strategy_name_to_use == "mirror_price":
        strategy_instance = MirrorPriceStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            binance_client=binance_client_instance,
            logger=app.logger
        )
        # MP策略已在内部处理API凭证，不需要额外设置
        
    # elif strategy_name_to_use == "raydium_simple":
    #     # RadiumSimpleMarketMaker 策略已被删除
    #     raise ValueError("raydium_simple策略已不可用，请使用其他策略")
        
    elif strategy_name_to_use == "gradient_liquidity":
        strategy_instance = GradientLiquidityStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        
    elif strategy_name_to_use == "adaptive_kline":
        strategy_instance = AdaptiveKlineStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("NINE_API_KEY")
        strategy_instance.api_secret = config.get("NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )

    elif strategy_name_to_use == "active":
        strategy_instance = ActiveStrategy(
            config=dict(config),
            nine_client=nine_client_instance,
            logger=app.logger
        )
        # 设置策略的API凭证用于交易对管理器
        strategy_instance.api_key = config.get("NINE_API_KEY")
        strategy_instance.api_secret = config.get("NINE_API_SECRET")
        strategy_instance.trading_pair_manager.set_default_credentials(
            strategy_instance.api_key, strategy_instance.api_secret
        )
    else:
        app.logger.error(f"不支持的交易策略: {strategy_name_to_use}")
        return None
    
    # Initialize TradingBotService with the selected strategy and shared NineClient
    # The config passed to TradingBotService is the app.config, which contains ALL settings.
    # TradingBotService itself no longer directly uses API keys from this config for its operations.
    bot_service = TradingBotService(
        nine_client=nine_client_instance, # Use shared NineClient
        strategy=strategy_instance,
        config=dict(config), 
        logger=app.logger,
        binance_client=binance_client_instance  # Pass BinanceClient for arbitrage strategies
    )
    return bot_service 