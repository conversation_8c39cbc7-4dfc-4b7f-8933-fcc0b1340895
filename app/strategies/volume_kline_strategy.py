import time
import decimal
from typing import List, Dict, Any, Optional
from decimal import InvalidOperation
import logging

from .base_strategy import BaseStrategy
from app.services.nine_client import NineClient
from app.services.order_management_service import get_oms, generate_stable_strategy_id
# BinanceClient is no longer directly needed for this strategy's core logic
# from app.services.binance_client import BinanceClient

class VolumeKlineStrategy(BaseStrategy):
    """
    成交量K线策略 - 定时定量刷量策略
    
    核心逻辑：
    1. 获取Nine CEX盘口最佳买卖价格（仅第一档）
    2. 在最佳买价下买单，在最佳卖价下卖单
    3. 定时执行，增加交易对成交量
    """
    def __init__(
        self,
        config: Dict[str, Any],
        nine_client: NineClient,
        logger: logging.Logger
    ):
        super().__init__(config, nine_client, logger)
        # self.binance_client = binance_client # Removed
        # if not self.binance_client: # Removed
        #     raise ValueError("Binance client is required for VolumeKlineStrategy.")
        self.last_execution_time: float = 0.0
        # VK_INITIAL_DIRECTION is no longer needed as we place both buy and sell
        # initial_direction_str = str(self._get_config_value("VK_INITIAL_DIRECTION", "buy", str)).lower()
        # if initial_direction_str not in ["buy", "sell"]:
        #     raise ValueError("VK_INITIAL_DIRECTION must be 'buy' or 'sell'")
        # self.next_direction: str = initial_direction_str

        # Load API keys - 优先使用通用 NINE_API_KEY，如果不存在则使用策略特定的 VK_NINE_API_KEY
        # 这样既保持向后兼容性，又支持使用通用配置
        api_key_candidates = [
            self._get_config_value("NINE_API_KEY", None),
            self._get_config_value("VK_NINE_API_KEY", None)
        ]
        api_secret_candidates = [
            self._get_config_value("NINE_API_SECRET", None),
            self._get_config_value("VK_NINE_API_SECRET", None)
        ]

        # 选择第一个非空的API凭证
        self.api_key = next((key for key in api_key_candidates if key), None)
        self.api_secret = next((secret for secret in api_secret_candidates if secret), None)

        # 验证API凭证是否获取成功
        if not self.api_key or not self.api_secret:
            raise ValueError("API凭证配置缺失: 请在 .env 文件中配置 NINE_API_KEY 和 NINE_API_SECRET，或在策略配置文件中配置 VK_NINE_API_KEY 和 VK_NINE_API_SECRET")
        
        # 初始化日志已在 BaseStrategy 中统一处理
        
        # 新增：声明订单簿获取参数
        self.fetch_nine_book_symbol = self._get_config_value("VK_TRADING_PAIR", expected_type=str)
        self.fetch_nine_book_precision = float(self._get_config_value("VK_NINE_POOL_PRECISION_API_PARAM", "0.01", str))
        self.fetch_nine_book_depth = 5  # Nine CEX API要求最小值为5

        self.last_execution_time = 0
        self.last_api_error_time = 0
        self.api_error_cooldown = 60  # 60秒冷却时间

        # 配置验证和调试信息
        self._validate_configuration()

        # OMS集成
        self.order_management_service = get_oms()
        self.strategy_id = generate_stable_strategy_id(self)

        # 首次运行标记 - 用于启动时撤销所有订单
        self.is_first_run = True

    def _validate_configuration(self):
        """验证 VolumeKlineStrategy 配置"""
        try:
            # 验证关键配置项
            trading_pair = self._get_config_value("VK_TRADING_PAIR", expected_type=str)
            order_amount = self._get_config_value("VK_ORDER_AMOUNT", expected_type=str)
            min_trade_qty = self._get_config_value("VK_MIN_TRADE_QTY_ASSET", expected_type=str)

            # 验证 API 凭证存在性 - 检查已经在 __init__ 中设置的 api_key 和 api_secret
            if not hasattr(self, 'api_key') or not self.api_key:
                raise ValueError("API Key 未正确配置")
            if not hasattr(self, 'api_secret') or not self.api_secret:
                raise ValueError("API Secret 未正确配置")

            # 验证精度配置
            price_precision = self._get_config_value("VK_PRICE_PRECISION", expected_type=int)
            qty_precision = self._get_config_value("VK_QTY_PRECISION", expected_type=int)

            # 验证通用Nine CEX参数
            self._get_config_value("NINE_ACCOUNT_TYPE", expected_type=int)
            self._get_config_value("NINE_ORDER_DIR_BUY", expected_type=int)
            self._get_config_value("NINE_ORDER_DIR_SELL", expected_type=int)
            self._get_config_value("NINE_ORDER_TYPE_LIMIT", expected_type=int)

            # 验证订单簿配置
            pool_precision = self._get_config_value("VK_NINE_POOL_PRECISION_API_PARAM", "0.01", str)

            self.logger.info(f"✅ [VK策略] 配置验证完成")
            self.logger.info(f"   📊 交易对: {trading_pair}")
            self.logger.info(f"   💰 订单数量: {order_amount}")
            self.logger.info(f"   🎯 最小交易量: {min_trade_qty}")
            self.logger.info(f"   🔧 价格精度: {price_precision}, 数量精度: {qty_precision}")
            self.logger.info(f"   📈 订单簿精度: {pool_precision}")

        except ValueError as e:
            self.logger.error(f"❌ [VK策略] 配置验证失败: {e}")
            raise
        except Exception as e:
            self.logger.error(f"❌ [VK策略] 配置验证异常: {e}")
            raise ValueError(f"配置验证失败: {e}")

    def _handle_first_run_cleanup(self, current_active_orders: List[Dict]) -> Optional[List[Dict]]:
        """
        处理首次运行清理逻辑 - 只清理本策略的订单

        Args:
            current_active_orders: 当前活跃订单列表 (来自OMS，已过滤到本策略)

        Returns:
            Optional[List[Dict]]: 如果需要返回清理动作则返回动作列表，否则返回None
        """
        if not self.is_first_run:
            return None

        self.logger.info("🚀 [VK策略] 首次运行，初始化策略状态")
        actions = []

        # 1. 清理本策略在OMS中的缓存状态
        if hasattr(self, 'order_management_service') and self.order_management_service:
            try:
                cleared_count = self.order_management_service.clear_strategy_active_orders(self.strategy_id)
                if cleared_count > 0:
                    self.logger.info(f"📚 [VK策略] 清理本策略历史缓存: {cleared_count}个订单")
            except Exception as e:
                self.logger.warning(f"⚠️ [VK策略] OMS缓存清理失败: {e}")

        # 2. 只处理OMS识别的本策略订单
        if current_active_orders:
            order_ids = [order.get("orderId") for order in current_active_orders if order.get("orderId")]
            if order_ids:
                actions.append({
                    "action_type": "cancel",
                    "reason": f"[VK策略] 清理本策略({self.strategy_id[:8]})历史订单",
                    "order_ids": order_ids
                })
                self.logger.info(f"📝 [VK策略] 准备撤销本策略的 {len(order_ids)} 个历史订单")
        else:
            self.logger.info("✨ [VK策略] 本策略无历史订单，开始全新运行")

        self.is_first_run = False
        return actions if actions else None

    def get_actions(self,
                    current_active_orders: Optional[List[Dict]] = None, 
                    nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        刷量策略核心逻辑：
        1. 首次运行清理
        2. 检查执行间隔
        3. 撤销旧订单
        4. 在盘口最佳价格下单（买单+卖单）
        """
        actions: List[Dict[str, Any]] = []
        current_time = time.time()

        # 首次运行清理逻辑（一次性执行）
        first_run_action = self._handle_first_run_cleanup(current_active_orders or [])
        if first_run_action:
            return first_run_action

        # 获取策略配置
        trading_pair = str(self._get_config_value("VK_TRADING_PAIR", expected_type=str))
        
        # 固定下单数量（基础资产）
        order_quantity_base_str = str(self._get_config_value("VK_ORDER_AMOUNT", expected_type=str))
        desired_order_quantity_base = decimal.Decimal(order_quantity_base_str)
        
        # 最小交易数量阈值
        min_trade_qty_asset_str = str(self._get_config_value("VK_MIN_TRADE_QTY_ASSET", expected_type=str))
        min_trade_qty_asset = decimal.Decimal(min_trade_qty_asset_str)
        if min_trade_qty_asset <= decimal.Decimal("0"):
            raise ValueError("VK_MIN_TRADE_QTY_ASSET must be positive.")

        # Nine CEX订单参数
        account_type = self._get_config_value("NINE_ACCOUNT_TYPE", expected_type=int)
        order_direction_buy = self._get_config_value("NINE_ORDER_DIR_BUY", expected_type=int)
        order_direction_sell = self._get_config_value("NINE_ORDER_DIR_SELL", expected_type=int)
        order_type_limit = self._get_config_value("NINE_ORDER_TYPE_LIMIT", expected_type=int)

        # 精度配置
        price_precision = self._get_config_value("VK_PRICE_PRECISION", expected_type=int)
        qty_precision = self._get_config_value("VK_QTY_PRECISION", expected_type=int)
        
        # 2. 撤销旧订单（刷量策略每次都重新下单）
        if current_active_orders:
            order_ids_to_cancel = [order['orderId'] for order in current_active_orders if order.get('orderId')]
            if order_ids_to_cancel:
                actions.append({
                    "action_type": "cancel",
                    "order_ids": order_ids_to_cancel,  # 使用标准字段名
                    "reason": "[VK策略] 撤销旧订单重新下单"
                })
                self.logger.info(f"📝 [VK策略] 准备撤销 {len(order_ids_to_cancel)} 个旧订单")

        # 3. 检查订单簿数据
        if not nine_cex_order_book_data:
            self.logger.error(f"❌ Nine CEX订单簿数据缺失")
            self.last_execution_time = current_time  # 更新时间避免频繁重试
            return actions

        # 4. 获取盘口最佳价格（仅第一档）
        # Nine CEX API 返回的数据结构：{"orderDepth": {"asks": [...], "bids": [...]}}
        order_depth = nine_cex_order_book_data.get("orderDepth", {})
        nine_asks = order_depth.get("asks", [])
        nine_bids = order_depth.get("bids", [])

        if not nine_asks or not nine_bids:
            self.logger.warning(f"⚠️ Nine CEX {trading_pair}盘口数据不全 - asks:{len(nine_asks)}, bids:{len(nine_bids)}")
            self.last_execution_time = current_time
            return actions

        self.logger.info(f"📊 [VK策略] 获取盘口数据成功 - asks:{len(nine_asks)}, bids:{len(nine_bids)}")

        orders_to_place = []

        # 5. 同时准备买单和卖单（真正的做市刷量策略）
        # 注意：需要账户中同时有SEPUSDT和SEPBTC余额
        
        # 准备买单（使用卖一价立即成交）
        if nine_asks and isinstance(nine_asks[0], list) and len(nine_asks[0]) >= 2:
            try:
                best_ask_price = decimal.Decimal(str(nine_asks[0][0]))  # 使用卖一价
                pool_best_ask_amount = decimal.Decimal(str(nine_asks[0][1]))

                if best_ask_price > decimal.Decimal("0") and pool_best_ask_amount > decimal.Decimal("0"):
                    # 下单数量：取固定数量和盘口数量的较小值
                    buy_qty_candidate = min(desired_order_quantity_base, pool_best_ask_amount)
                    
                    if buy_qty_candidate >= min_trade_qty_asset:
                        formatted_qty = self._format_quantity(buy_qty_candidate, qty_precision)
                        formatted_price = self._format_price(best_ask_price, price_precision)
                        
                        # 使用新的 create_trade_bean 方法，自动处理交易对ID转换
                        buy_order_data = self.create_trade_bean(
                            trading_pair=trading_pair,
                            order_direction=order_direction_buy,
                            order_quantity=formatted_qty,
                            order_price=formatted_price,
                            account_type=account_type,
                            order_type=order_type_limit
                        )
                        orders_to_place.append(buy_order_data)
            except (InvalidOperation, TypeError, IndexError) as e:
                self.logger.warning(f"⚠️ 处理买单数据异常: {e}")

        # 准备卖单（使用买一价立即成交）
        if nine_bids and isinstance(nine_bids[0], list) and len(nine_bids[0]) >= 2:
            try:
                best_bid_price = decimal.Decimal(str(nine_bids[0][0]))  # 使用买一价
                pool_best_bid_amount = decimal.Decimal(str(nine_bids[0][1]))

                if best_bid_price > decimal.Decimal("0") and pool_best_bid_amount > decimal.Decimal("0"):
                    # 下单数量：取固定数量和盘口数量的较小值
                    sell_qty_candidate = min(desired_order_quantity_base, pool_best_bid_amount)

                    if sell_qty_candidate >= min_trade_qty_asset:
                        formatted_qty = self._format_quantity(sell_qty_candidate, qty_precision)
                        formatted_price = self._format_price(best_bid_price, price_precision)
                        
                        # 使用新的 create_trade_bean 方法，自动处理交易对ID转换
                        sell_order_data = self.create_trade_bean(
                            trading_pair=trading_pair,
                            order_direction=order_direction_sell,
                            order_quantity=formatted_qty,
                            order_price=formatted_price,
                            account_type=account_type,
                            order_type=order_type_limit
                        )
                        orders_to_place.append(sell_order_data)
            except (InvalidOperation, TypeError, IndexError) as e:
                self.logger.warning(f"⚠️ 处理卖单数据异常: {e}")
        
        # 7. 提交订单
        if orders_to_place:
            actions.append({
                "action_type": "place",
                "orders": orders_to_place,
                "reason": f"刷量策略: 在盘口最佳价格下{len(orders_to_place)}个订单"
            })
            self.logger.info(f"🎯 [VK策略] 准备下单: {len(orders_to_place)} 个订单")

        # 8. 更新执行时间
        self.last_execution_time = current_time
        # self.next_direction is no longer needed

        return actions 