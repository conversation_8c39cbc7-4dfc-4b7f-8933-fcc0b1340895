from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
import decimal # Import the decimal module itself
import logging # Import logging
from app.services.trading_pair_manager import TradingPairManager

# A sentinel object to indicate a required configuration value
_REQUIRED = object()

class BaseStrategy(ABC):

    def __init__(self, config: Dict[str, Any], nine_client: Any, logger: Optional[logging.Logger] = None):
        self.config = config
        self.nine_client = nine_client
        # Set strategy name based on class name
        self.strategy_name = self.__class__.__name__
        
        # Use provided logger or create a default one for the strategy
        if logger:
            self.logger = logger
        else:
            self.logger = logging.getLogger(self.__class__.__name__)
            # Basic configuration if no logger is passed and a new one is created
            # This might lead to duplicate handlers if app already configures root logger
            # Consider if strategies should *always* receive a configured logger
            if not self.logger.handlers:
                handler = logging.StreamHandler()
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
                self.logger.setLevel(logging.INFO)
        
        # 初始化交易对管理器
        self.trading_pair_manager = TradingPairManager(nine_client, self.logger)
        
        # 如果策略有API凭证，设置为交易对管理器的默认凭证
        if hasattr(self, 'api_key') and hasattr(self, 'api_secret') and self.api_key and self.api_secret:
            self.trading_pair_manager.set_default_credentials(self.api_key, self.api_secret)

    def resolve_trading_pair_id(self, trading_pair: str, api_key: Optional[str] = None, api_secret: Optional[str] = None) -> str:
        """
        解析交易对标识符，自动转换名称为ID
        
        Args:
            trading_pair: 交易对标识符（名称如'SEPBTC/SEPUSDT'或ID如'20250617000029'）
            api_key: API密钥，如果不提供则使用策略默认值
            api_secret: API密钥，如果不提供则使用策略默认值
            
        Returns:
            str: 交易对ID，如果无法解析则返回原始值
        """
        # 使用提供的凭证或策略自身的凭证
        use_api_key = api_key or getattr(self, 'api_key', None)
        use_api_secret = api_secret or getattr(self, 'api_secret', None)
        
        try:
            resolved_id = self.trading_pair_manager.resolve_pair_identifier(
                trading_pair, use_api_key, use_api_secret
            )
            
            if resolved_id != trading_pair:
                self.logger.debug(f"🔄 [策略:{self.strategy_name}] 交易对转换: {trading_pair} -> {resolved_id}")
            
            return resolved_id
            
        except Exception as e:
            self.logger.warning(f"⚠️ [策略:{self.strategy_name}] 交易对解析失败: {trading_pair}, 错误: {e}")
            return trading_pair  # 返回原始值作为后备

    def get_trading_pair_info(self, trading_pair: str) -> Optional[Dict[str, Any]]:
        """
        获取交易对的完整信息
        
        Args:
            trading_pair: 交易对标识符（名称或ID）
            
        Returns:
            dict: 交易对信息，包含价格、数量等详细数据
        """
        try:
            # 先解析为ID
            pair_id = self.resolve_trading_pair_id(trading_pair)
            
            # 如果是ID格式，获取信息
            if self.trading_pair_manager.is_pair_id(pair_id):
                return self.trading_pair_manager.get_pair_info(pair_id)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"⚠️ [策略:{self.strategy_name}] 获取交易对信息失败: {trading_pair}, 错误: {e}")
            return None

    def create_trade_bean(self,
                         trading_pair: str,
                         order_direction: int,
                         order_quantity: str,
                         order_price: str,
                         account_type: Optional[int] = None,
                         order_type: Optional[int] = None,
                         pair_id: Optional[str] = None) -> Dict[str, Any]:
        """
        创建标准的 tradeBean 结构，自动处理交易对ID转换
        
        Args:
            trading_pair: 交易对标识符（会自动转换为ID）
            order_direction: 订单方向（1=买，2=卖）
            order_quantity: 订单数量
            order_price: 订单价格
            account_type: 账户类型，默认使用配置值
            order_type: 订单类型，默认使用配置值
            
        Returns:
            dict: 标准的 tradeBean 结构
        """
        # 使用传入的pair_id，否则解析交易对ID（向后兼容）
        resolved_pair_id = pair_id or self.resolve_trading_pair_id(trading_pair)
        
        # 获取配置中的默认值
        default_account_type = self._get_config_value('NINE_ACCOUNT_TYPE', 1, int)
        default_order_type = self._get_config_value('NINE_ORDER_TYPE_LIMIT', 1, int)
        
        # tradePairId必须从getOuterPoolInfo接口获取正确的ID，严格验证
        if not self.trading_pair_manager.is_pair_id(resolved_pair_id) or resolved_pair_id == trading_pair:
            # 无法获取正确的交易对ID，直接报错
            error_msg = f"无法获取交易对 {trading_pair} 的正确ID。tradePairId必须从getOuterPoolInfo接口获取"
            self.logger.error(f"❌ [策略:{self.strategy_name}] {error_msg}")
            raise ValueError(error_msg)
        
        # 构建tradeBean，只有在获得正确ID时才创建
        trade_bean = {
            "accountType": account_type or default_account_type,
            "tradePairName": trading_pair,  # 保留原始名称用于日志
            "tradePairId": resolved_pair_id,  # 只使用从API获取的正确ID
            "orderDirection": order_direction,
            "orderType": order_type or default_order_type,
            "orderQuantity": order_quantity,
            "orderPrice": order_price
        }
        # 验证所有必需字段都存在且非空
        required_fields = ["accountType", "tradePairId", "orderDirection", "orderType", "orderQuantity", "orderPrice"]
        for field in required_fields:
            value = trade_bean.get(field)
            if value is None or str(value).strip() == "":
                error_msg = f"tradeBean字段'{field}'为空或无效: {value}"
                self.logger.error(f"❌ {error_msg}")
                raise ValueError(error_msg)
        
        return trade_bean

    @abstractmethod
    def get_actions(self, current_active_orders: List[Dict], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        根据当前市场情况和活动订单，决定交易行为。

        Args:
            current_active_orders: 当前在 Nine CEX 上由本服务下的活动订单列表。
                                   每个字典至少包含: {'orderId': str, 'price': decimal.Decimal, 'original_quantity': decimal.Decimal, 'direction': int}
                                   具体内容取决于 TradingBotService.nine_cex_order_data 的结构。
            nine_cex_order_book_data: 可选的 Nine CEX 订单簿数据，用于减少重复API调用

        Returns:
            List[Dict[str, Any]]: 一个操作组列表。
            每个操作组是一个字典，结构应为：
            - {"action_type": "place", "reason": str, "orders": List[Dict]} 
              (其中 orders 列表中的每个字典是构建 Nine CEX tradeBean 所需的下单参数)
            - {"action_type": "cancel", "reason": str, "orders": List[str]} 
              (其中 orders 列表是需要撤销的订单ID)
            如果返回空列表，则本轮不执行任何操作。
        """
        pass

    def _get_config_value(self, key: str, default_value: Any = _REQUIRED, expected_type: Optional[type] = None) -> Any:
        """辅助方法：从 self.config 安全地获取配置值，支持默认值和类型转换。
        如果 default_value 为 _REQUIRED (默认)，则该配置项为必需。"""
        value = self.config.get(key)

        if value is None or str(value).strip() == "": # Treat empty strings as None for config values
            if default_value is _REQUIRED:
                self.logger.error(f"Strategy 配置缺失: 必需的键 '{key}' 未找到或为空。")
                raise ValueError(f"Strategy 配置缺失: 必需的键 '{key}'")
            return default_value # Return the provided default_value (could be None or something else)

        if expected_type:
            try:
                if expected_type == decimal.Decimal:
                    # Ensure value is string before Decimal conversion for consistency
                    return decimal.Decimal(str(value))
                return expected_type(value)
            except (TypeError, ValueError) as e:
                self.logger.error(f"Strategy 配置参数 '{key}' (值: '{value}') 类型转换失败 (目标类型 {expected_type}): {e}")
                raise ValueError(f"配置参数 '{key}' 类型错误: {e}")
        return value

    def _format_price(self, price_input: decimal.Decimal, precision: int) -> str:
        """根据指定的精度格式化价格 Decimal 对象为字符串。"""
        quantizer = decimal.Decimal('1e-' + str(precision))
        formatted_price = price_input.quantize(quantizer, rounding=decimal.ROUND_DOWN)
        return str(formatted_price)

    def _format_quantity(self, quantity_input: decimal.Decimal, precision: int) -> str:
        """根据指定的精度格式化数量 Decimal 对象为字符串 (向下取整)。"""
        quantizer = decimal.Decimal('1e-' + str(precision))
        formatted_qty = quantity_input.quantize(quantizer, rounding=decimal.ROUND_DOWN)
        return str(formatted_qty)

    def get_trading_pair_balances(self) -> Dict[str, float]:
        """
        获取当前交易对相关的资产余额
        
        Returns:
            Dict[str, float]: {"base_asset": amount, "quote_asset": amount}
            例如: {"二哈": 1000.0, "USDT": 500.0}
        """
        try:
            # 解析交易对
            if not hasattr(self, 'trading_pair') or '/' not in self.trading_pair:
                return {}
            
            base_asset, quote_asset = self.trading_pair.split('/')
            
            # 获取完整余额
            balance_result = self.nine_client.get_wallet_account(
                user_api_key=getattr(self, 'api_key', ''),
                user_api_secret=getattr(self, 'api_secret', '')
            )
            
            if not (balance_result and balance_result.get('code') == 200):
                return {}
            
            # 提取交易对相关资产
            token_list = balance_result.get('data', {}).get('fundingBalanceInfo', {}).get('tokenList', [])
            pair_balances = {}
            
            for token in token_list:
                asset = token.get('asset', '')
                if asset in [base_asset, quote_asset]:
                    try:
                        total_amount = float(token.get('amount', 0))
                        available_amount = float(token.get('availableAmount', 0))
                        pair_balances[asset] = {
                            'total': total_amount,
                            'available': available_amount,
                            'locked': total_amount - available_amount
                        }
                    except (ValueError, TypeError):
                        pair_balances[asset] = {'total': 0.0, 'available': 0.0, 'locked': 0.0}
            
            return pair_balances
            
        except Exception as e:
            self.logger.debug(f"获取交易对余额异常: {e}")
            return {} 