"""
简单活跃策略 (ActiveStrategy)

专注于制造盘口活跃度的纯粹策略：
- 本地OMS订单生命周期管理
- 随机撤销现有订单
- 随机挂新订单
- 保持订单簿持续变化

遵循 KISS 原则，代码极简，职责单一
"""

from typing import List, Dict, Optional
import logging
import random
import time
from decimal import Decimal
from app.strategies.base_strategy import BaseStrategy


class ActiveStrategy(BaseStrategy):
    """简单活跃策略 - 专注于制造盘口活跃度"""
    
    def __init__(self, config: dict, nine_client, logger: Optional[logging.Logger] = None):
        super().__init__(config, nine_client, logger)

        # API凭证从主配置文件读取（不是策略配置文件）
        self.api_key = config.get("NINE_API_KEY")
        self.api_secret = config.get("NINE_API_SECRET")

        if not self.api_key or not self.api_secret:
            raise ValueError("NINE_API_KEY 和 NINE_API_SECRET 必须在主目录 .env 文件中设置")
            
        # 基础配置
        self.trading_pair = self._get_config_value("ACTIVE_TRADING_PAIR", expected_type=str)
        self.update_interval = self._get_config_value("ACTIVE_UPDATE_INTERVAL", 10, int)
        
        # 活跃度配置
        self.cancel_ratio = self._get_config_value("ACTIVE_CANCEL_RATIO", Decimal("0.3"), Decimal)
        self.place_count = self._get_config_value("ACTIVE_PLACE_COUNT", 3, int)
        self.price_range = self._get_config_value("ACTIVE_PRICE_RANGE", Decimal("0.02"), Decimal)  # ±2%
        self.order_amount = self._get_config_value("ACTIVE_ORDER_AMOUNT", Decimal("1.0"), Decimal)
        
        # 精度配置
        self.price_precision = self._get_config_value("ACTIVE_PRICE_PRECISION", 8, int)
        self.qty_precision = self._get_config_value("ACTIVE_QTY_PRECISION", 6, int)

        # 订单生命周期管理配置
        self.order_max_age = self._get_config_value("ACTIVE_ORDER_MAX_AGE", 30, int)  # 订单最大存活时间(秒)
        self.max_orders = self._get_config_value("ACTIVE_MAX_ORDERS", 20, int)  # 最大订单数

        # 本地OMS：订单生命周期跟踪
        self.local_orders = {}  # {order_id: {"timestamp": time, "price": Decimal, "direction": int}}

        # 缓存交易对ID
        self.trade_pair_id_cached = None
        try:
            self.trade_pair_id_cached = self.resolve_trading_pair_id(self.trading_pair)
            self.logger.info(f"🎲 活跃策略启动: {self.trading_pair}, 间隔{self.update_interval}秒, 订单存活{self.order_max_age}秒")
        except Exception as e:
            self.logger.error(f"❌ 交易对ID解析失败: {e}")
    
    def get_actions(self, current_active_orders: List[Dict], nine_cex_order_book_data: Optional[Dict] = None) -> List[Dict]:
        """获取策略动作 - 基于OMS生命周期管理的随机挂撤"""
        actions = []

        try:
            # 1. 更新本地OMS状态
            self._update_local_oms(current_active_orders)

            # 2. 获取当前价格（必须从真实盘口获取）
            current_price = self._get_current_price(current_active_orders, nine_cex_order_book_data)
            if not current_price or current_price <= 0:
                self.logger.warning("⚠️ 无法获取有效价格，跳过本轮执行")
                return actions

            # 3. 生命周期管理：撤销老化订单
            lifecycle_cancel_actions = self._create_lifecycle_cancel_actions(current_active_orders)
            if lifecycle_cancel_actions:
                actions.extend(lifecycle_cancel_actions)

            # 4. 随机撤单（在剩余订单中）
            remaining_orders = self._get_remaining_orders(current_active_orders, lifecycle_cancel_actions)
            random_cancel_actions = self._create_random_cancel_actions(remaining_orders)
            if random_cancel_actions:
                actions.extend(random_cancel_actions)

            # 5. 智能挂单（考虑订单数量限制）
            place_actions = self._create_smart_place_actions(current_price, current_active_orders, actions)
            if place_actions:
                actions.extend(place_actions)

        except Exception as e:
            self.logger.error(f"❌ 活跃策略执行异常: {e}")

        return actions
    
    def _get_current_price(self, current_active_orders: List[Dict], order_book_data: Optional[Dict]) -> Optional[Decimal]:
        """获取当前参考价格 - 必须从真实盘口获取"""
        # 方法1: 从订单簿获取中间价（优先）
        if order_book_data:
            try:
                asks = order_book_data.get("asks", [])
                bids = order_book_data.get("bids", [])
                if asks and bids and len(asks) > 0 and len(bids) > 0:
                    best_ask = Decimal(str(asks[0][0]))
                    best_bid = Decimal(str(bids[0][0]))
                    if best_ask > 0 and best_bid > 0:
                        return (best_ask + best_bid) / Decimal("2")
            except Exception as e:
                self.logger.warning(f"⚠️ 解析订单簿失败: {e}")

        # 方法2: 从现有订单推断价格
        if current_active_orders:
            try:
                prices = []
                for order in current_active_orders:
                    price = order.get('price')
                    if price and Decimal(str(price)) > 0:
                        prices.append(Decimal(str(price)))

                if prices:
                    # 使用中位数价格，更稳定
                    prices.sort()
                    mid_idx = len(prices) // 2
                    if len(prices) % 2 == 0:
                        return (prices[mid_idx-1] + prices[mid_idx]) / Decimal("2")
                    else:
                        return prices[mid_idx]
            except Exception as e:
                self.logger.warning(f"⚠️ 从订单推断价格失败: {e}")

        # 无法获取有效价格，返回None
        self.logger.error("❌ 无法获取当前价格：订单簿为空且无现有订单")
        return None

    def _update_local_oms(self, current_active_orders: List[Dict]):
        """更新本地OMS状态"""
        current_time = time.time()
        current_order_ids = {order.get('orderId') for order in current_active_orders if order.get('orderId')}

        # 清理已不存在的订单
        to_remove = []
        for order_id in self.local_orders:
            if order_id not in current_order_ids:
                to_remove.append(order_id)

        for order_id in to_remove:
            del self.local_orders[order_id]

        # 添加新订单到本地跟踪
        for order in current_active_orders:
            order_id = order.get('orderId')
            if order_id and order_id not in self.local_orders:
                self.local_orders[order_id] = {
                    "timestamp": current_time,
                    "price": Decimal(str(order.get('price', 0))),
                    "direction": order.get('direction', 1)
                }

    def _create_lifecycle_cancel_actions(self, current_active_orders: List[Dict]) -> List[Dict]:
        """基于生命周期管理创建撤单动作"""
        current_time = time.time()
        aged_order_ids = []

        for order in current_active_orders:
            order_id = order.get('orderId')
            if order_id in self.local_orders:
                age = current_time - self.local_orders[order_id]["timestamp"]
                if age > self.order_max_age:
                    aged_order_ids.append(order_id)

        if aged_order_ids:
            self.logger.info(f"⏰ 生命周期撤单: {len(aged_order_ids)}个老化订单")
            return [{
                "action_type": "cancel",
                "reason": f"生命周期管理撤销{len(aged_order_ids)}个老化订单",
                "order_ids": aged_order_ids
            }]

        return []

    def _get_remaining_orders(self, current_active_orders: List[Dict], cancel_actions: List[Dict]) -> List[Dict]:
        """获取扣除即将撤销订单后的剩余订单"""
        if not cancel_actions:
            return current_active_orders

        # 收集所有即将撤销的订单ID
        cancel_ids = set()
        for action in cancel_actions:
            if action.get("action_type") == "cancel":
                cancel_ids.update(action.get("order_ids", []))

        # 返回不在撤销列表中的订单
        return [order for order in current_active_orders if order.get('orderId') not in cancel_ids]
    
    def _create_random_cancel_actions(self, remaining_orders: List[Dict]) -> List[Dict]:
        """在剩余订单中创建随机撤单动作"""
        if not remaining_orders:
            return []

        # 随机选择要撤销的订单（在剩余订单中）
        cancel_count = max(1, int(len(remaining_orders) * float(self.cancel_ratio)))
        orders_to_cancel = random.sample(remaining_orders, min(cancel_count, len(remaining_orders)))
        cancel_ids = [o.get('orderId') for o in orders_to_cancel if o.get('orderId')]

        if cancel_ids:
            self.logger.info(f"🎲 随机撤销 {len(cancel_ids)} 个订单")
            return [{
                "action_type": "cancel",
                "reason": f"随机撤销{len(cancel_ids)}单",
                "order_ids": cancel_ids
            }]

        return []
    
    def _create_smart_place_actions(self, base_price: Decimal, current_active_orders: List[Dict], pending_actions: List[Dict]) -> List[Dict]:
        """创建智能挂单动作（考虑订单数量限制）"""
        # 计算撤销后的预期订单数量
        cancel_count = 0
        for action in pending_actions:
            if action.get("action_type") == "cancel":
                cancel_count += len(action.get("order_ids", []))

        expected_order_count = len(current_active_orders) - cancel_count
        available_slots = self.max_orders - expected_order_count

        if available_slots <= 0:
            self.logger.info(f"📊 订单数量已达上限({self.max_orders})，跳过挂单")
            return []

        # 限制挂单数量
        actual_place_count = min(self.place_count, available_slots)
        orders = []

        for _ in range(actual_place_count):
            # 随机方向
            direction = random.choice([1, 2])  # 1=买, 2=卖

            # 随机价格偏移
            price_offset = Decimal(str(random.uniform(-float(self.price_range), float(self.price_range))))
            order_price = base_price * (Decimal("1") + price_offset)

            # 确保最小订单金额
            order_amount = max(self.order_amount * Decimal(str(random.uniform(0.5, 1.5))), Decimal("0.1"))
            order_qty = order_amount / order_price

            # 格式化
            formatted_price = self._format_price(order_price, self.price_precision)
            formatted_qty = self._format_quantity(order_qty, self.qty_precision)

            # 创建订单
            try:
                order = self.create_trade_bean(
                    trading_pair=self.trading_pair,
                    order_direction=direction,
                    order_quantity=formatted_qty,
                    order_price=formatted_price,
                    pair_id=self.trade_pair_id_cached
                )
                orders.append(order)
            except Exception as e:
                self.logger.warning(f"⚠️ 创建随机订单失败: {e}")
                continue

        if orders:
            self.logger.info(f"🎲 智能挂单 {len(orders)} 个 (可用槽位:{available_slots})")
            return [{
                "action_type": "place",
                "reason": f"智能挂{len(orders)}单",
                "orders": orders
            }]

        return []

    def _format_price(self, price: Decimal, precision: int) -> str:
        """格式化价格"""
        return f"{price:.{precision}f}".rstrip('0').rstrip('.')

    def _format_quantity(self, quantity: Decimal, precision: int) -> str:
        """格式化数量"""
        return f"{quantity:.{precision}f}".rstrip('0').rstrip('.')
