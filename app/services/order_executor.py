# app/services/order_executor.py

from .nine_client import NineClient
from .order_management_service import get_oms, generate_stable_strategy_id
from decimal import Decimal, InvalidOperation as DecimalInvalidOperation
from typing import List, Dict, Optional, Any
import logging


class OrderExecutor:
    """订单执行器 - 专门负责订单的创建和撤销"""
    
    def __init__(self, nine_client: NineClient, strategy, config: dict, logger: logging.Logger):
        """
        初始化订单执行器
        
        Args:
            nine_client: Nine CEX客户端
            strategy: 交易策略实例
            config: 配置字典
            logger: 日志记录器
        """
        self.nine_client = nine_client
        self.strategy = strategy
        self.config = config
        self.logger = logger
        self.oms = get_oms()
        
        # 生成策略ID
        self.strategy_id = generate_stable_strategy_id(strategy)
        
    def get_conflict_tolerance(self) -> Decimal:
        """获取订单冲突检测的价格容忍度"""
        try:
            # 优先从策略配置获取
            if hasattr(self.strategy, 'conflict_tolerance'):
                return Decimal(str(self.strategy.conflict_tolerance))
            
            # 从环境配置获取
            tolerance_str = self.config.get('ORDER_CONFLICT_TOLERANCE', '0.001')
            return Decimal(str(tolerance_str))
            
        except (ValueError, TypeError) as e:
            self.logger.warning(f"⚠️ 冲突容忍度配置无效，使用默认值0.1%: {e}")
            return Decimal("0.001")  # 默认0.1%
    
    def execute_placements(self, orders_to_place: List[Dict], dry_run: bool = False) -> bool:
        """
        执行在Nine CEX的批量下单操作
        
        Args:
            orders_to_place: 由策略生成的符合Nine CEX tradeBean结构的订单列表
            dry_run: 是否为模拟模式，True时不实际执行API调用
            
        Returns:
            bool: 操作是否成功 (API调用层面)
        """
        if not orders_to_place:
            return True
        
        # 模拟模式：只打印操作，不执行实际API调用
        if dry_run:
            self.logger.info(f"🎭 [模拟模式] 将下达 {len(orders_to_place)} 个订单:")
            for i, order in enumerate(orders_to_place, 1):
                direction_text = "买入" if order['orderDirection'] == 1 else "卖出"
                qty = float(order['orderQuantity'])
                price = float(order['orderPrice'])
                usdt_amount = qty * price
                self.logger.info(f"  📝 订单#{i}: {direction_text} {order['orderQuantity']} @ {order['orderPrice']} = {usdt_amount:.2f} USDT")
            self.logger.info(f"🎭 [模拟模式] 下单操作完成 (未实际执行)")
            return True

        # API keys validation
        if not self.strategy.api_key or not self.strategy.api_secret:
            self.logger.error(f"策略 '{self.strategy.__class__.__name__}' API Key 或 Secret 未配置，无法下单")
            return False

        # 订单冲突检测
        if not self._check_order_conflicts(orders_to_place):
            return False

        # 构造批量交易请求
        batch_trade_payload = []
        for order in orders_to_place:
            batch_trade_payload.append({
                "userBean": {"apiKey": self.strategy.api_key, "secret": self.strategy.api_secret},
                "tradeBean": [order]
            })

        self.logger.info(f"📤 [NINE CEX] 发送 {len(orders_to_place)} 个订单批量请求...")

        # 执行API调用
        try:
            response_data = self.nine_client.batch_trade(trade_operations=batch_trade_payload)
        except ConnectionError as e:
            self.logger.error(f"❌ [NINE CEX] 连接失败: {e}")
            return False
        except ValueError as e:
            self.logger.error(f"❌ [NINE CEX] 参数错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 下单异常: {e}")
            return False

        # 处理响应
        return self._process_placement_response(response_data, orders_to_place)
    
    def _check_order_conflicts(self, orders_to_place: List[Dict]) -> bool:
        """检查订单冲突"""
        conflict_detected = False
        tolerance = self.get_conflict_tolerance()
        
        for order in orders_to_place:
            try:
                price = Decimal(str(order.get('orderPrice', '0')))
                trading_pair = order.get('tradePairName', '')
                
                conflicts = self.oms.check_order_conflicts(
                    self.strategy_id, 
                    trading_pair, 
                    price, 
                    tolerance
                )
                
                if conflicts:
                    conflict_detected = True
                    direction_text = "买入" if order.get('orderDirection') == 1 else "卖出"
                    self.logger.warning(f"⚠️ [订单冲突] {direction_text} {order.get('orderQuantity')} @ {price} 发现 {len(conflicts)} 个冲突:")
                    
                    for conflict in conflicts:
                        conflict_direction = "买入" if conflict['direction'] == 1 else "卖出"
                        self.logger.warning(f"  🔄 冲突订单: {conflict['strategy_type']} | {conflict_direction} @ {conflict['price']} | 价差: {conflict['price_diff']:.3%}")
                        
            except (ValueError, TypeError, DecimalInvalidOperation) as e:
                self.logger.error(f"❌ [订单冲突] 参数错误: {e}")
            except Exception as e:
                self.logger.error(f"❌ [订单冲突] 检测失败: {e}")
        
        if conflict_detected:
            # 对于分步拉升/下压订单，冲突是预期的，降低警告级别
            if any("分步" in order.get('reason', '') for order in orders_to_place):
                self.logger.info("ℹ️ [分步执行] 检测到价格重叠，属于分步拉升/下压的正常现象")
            else:
                self.logger.warning("⚠️ [订单冲突] 检测到冲突但继续执行下单，请注意市场影响")
        
        return True  # 允许继续执行
    
    def _process_placement_response(self, response_data: Dict, orders_to_place: List[Dict]) -> bool:
        """处理下单响应"""
        if not (response_data and isinstance(response_data, dict) and response_data.get("code") == 200 and response_data.get("msg") == "成功"):
            self.logger.error(f"❌ [NINE CEX] API响应错误")
            return False

        actual_order_results_list = response_data.get("data")
        if not isinstance(actual_order_results_list, list):
            self.logger.error(f"❌ [NINE CEX] 响应数据格式错误")
            return False

        if len(actual_order_results_list) != len(orders_to_place):
            self.logger.error(f"❌ [NINE CEX] 响应订单数量不匹配")
            return False
        
        all_items_processed_successfully = True
        successfully_placed_orders = []
        
        for i, trade_bean_result_wrapper in enumerate(actual_order_results_list):
            original_order_spec = orders_to_place[i]
            
            if not isinstance(trade_bean_result_wrapper, dict):
                self.logger.error(f"❌ [NINE CEX] 订单结果格式错误")
                all_items_processed_successfully = False
                continue
            
            inner_data_payload = trade_bean_result_wrapper.get("data")
            if not (isinstance(inner_data_payload, dict) and inner_data_payload.get("code") == 200):
                self._log_order_error(inner_data_payload, original_order_spec, i+1)
                all_items_processed_successfully = False
                continue
            
            order_id = inner_data_payload.get("data")
            if order_id and isinstance(order_id, str):
                # 先记录到OMS，确保数据一致性
                if self.oms.record_order_creation(order_id, self.strategy_id, original_order_spec):
                    successfully_placed_orders.append({
                        'orderId': order_id,
                        'price': Decimal(original_order_spec['orderPrice']),
                        'original_quantity': Decimal(original_order_spec['orderQuantity']),
                        'direction': original_order_spec['orderDirection'] 
                    })
                    
                    # 记录成功信息
                    self._log_order_success(order_id, original_order_spec)
                else:
                    self.logger.error(f"❌ [OMS] 订单 {order_id} 记录失败，跳过本地跟踪")
                    all_items_processed_successfully = False
            else:
                self.logger.error(f"❌ [NINE CEX] 订单未返回ID")
                all_items_processed_successfully = False
        
        self.logger.info(f"📊 [NINE CEX] 批量下单完成，成功{len(successfully_placed_orders)}个订单")
        
        if not all_items_processed_successfully:
            self.logger.warning("⚠️ [NINE CEX] 部分订单处理失败")
        
        return True
    
    def _log_order_error(self, inner_data_payload: Dict, original_order_spec: Dict, order_num: int):
        """记录订单错误信息"""
        if isinstance(inner_data_payload, dict):
            error_code = inner_data_payload.get("code")
            error_message = inner_data_payload.get("message", "未知错误")
            
            self.logger.error(f"🔍 [NINE CEX] 订单#{order_num}失败详情:")
            self.logger.error(f"   📋 错误码: {error_code}")
            self.logger.error(f"   📋 错误信息: {error_message}")
            self.logger.error(f"   📋 订单参数: {original_order_spec}")
            
            # 根据错误码提供友好错误信息
            if error_code == 3016:
                self.logger.error(f"💰 [NINE CEX] 订单#{order_num}失败: 账户余额不足 ({error_message})")
            elif error_code == 3001:
                self.logger.error(f"🔑 [NINE CEX] 订单#{order_num}失败: API认证错误 ({error_message})")
            elif error_code == 3002:
                self.logger.error(f"📊 [NINE CEX] 订单#{order_num}失败: 交易对不存在 ({error_message})")
            elif error_code == 3003:
                self.logger.error(f"📏 [NINE CEX] 订单#{order_num}失败: 订单参数错误 ({error_message})")
            elif error_code == 502:
                self.logger.error(f"🚫 [NINE CEX] 订单#{order_num}失败: 参数错误502 - 检查参数格式和完整性")
            else:
                self.logger.error(f"❌ [NINE CEX] 订单#{order_num}失败: 错误码{error_code} ({error_message})")
        else:
            self.logger.error(f"❌ [NINE CEX] 订单#{order_num}失败: 响应格式异常 ({inner_data_payload})")
    
    def _log_order_success(self, order_id: str, original_order_spec: Dict):
        """记录订单成功信息"""
        direction_text = "买入" if original_order_spec['orderDirection'] == 1 else "卖出"
        qty = float(original_order_spec['orderQuantity'])
        price = float(original_order_spec['orderPrice'])
        usdt_amount = qty * price
        self.logger.info(f"✅ [NINE CEX] 订单成功: ID#{order_id} | {direction_text} {original_order_spec['orderQuantity']} @ {original_order_spec['orderPrice']} = {usdt_amount:.2f} USDT")
    
    def execute_cancellations(self, order_ids_to_cancel: List[str], dry_run: bool = False) -> bool:
        """
        执行在Nine CEX的批量撤单操作
        
        Args:
            order_ids_to_cancel: 需要撤销的订单ID列表
            dry_run: 是否为模拟模式
            
        Returns:
            bool: 操作是否成功
        """
        if not order_ids_to_cancel:
            return True
        
        # 模拟模式
        if dry_run:
            self.logger.info(f"🎭 [模拟模式] 将撤销 {len(order_ids_to_cancel)} 个订单:")
            for i, order_id in enumerate(order_ids_to_cancel, 1):
                self.logger.info(f"  🚫 订单#{i}: {order_id}")
            self.logger.info(f"🎭 [模拟模式] 撤单操作完成 (未实际执行)")
            return True

        # API keys validation
        if not self.strategy.api_key or not self.strategy.api_secret:
            self.logger.error(f"策略 '{self.strategy.__class__.__name__}' API Key 或 Secret 未配置，无法进行撤单操作")
            return False

        self.logger.info(f"🚫 [NINE CEX] 批量撤销 {len(order_ids_to_cancel)} 个订单...")
        
        # 执行API调用
        try:
            cancel_response_data = self.nine_client.batch_cancel_orders(
                order_ids=order_ids_to_cancel,
                user_api_key=self.strategy.api_key,
                user_api_secret=self.strategy.api_secret
            )
        except ConnectionError as e:
            self.logger.error(f"❌ [NINE CEX] 撤单连接失败: {e}")
            return False
        except ValueError as e:
            self.logger.error(f"❌ [NINE CEX] 撤单参数错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 撤单异常: {e}")
            return False

        # 处理响应
        return self._process_cancellation_response(cancel_response_data, order_ids_to_cancel)
    
    def _process_cancellation_response(self, cancel_response_data: Dict, order_ids_to_cancel: List[str]) -> bool:
        """处理撤单响应"""
        if not (cancel_response_data and isinstance(cancel_response_data, dict) and cancel_response_data.get("code") == 200):
            self.logger.error(f"❌ [NINE CEX] 撤单API调用失败")
            return False 

        processed_outcomes = cancel_response_data.get("data", [])
        if not isinstance(processed_outcomes, list):
            self.logger.error(f"❌ [NINE CEX] 撤单响应格式错误")
            return False

        if len(processed_outcomes) != len(order_ids_to_cancel):
            self.logger.error(f"❌ [NINE CEX] 撤单响应数量不匹配")
            return False

        successfully_cancelled_ids = set()
        for i, outcome in enumerate(processed_outcomes):
            original_order_id = order_ids_to_cancel[i]
            if isinstance(outcome, dict):
                inner_data = outcome.get("data")
                if isinstance(inner_data, dict) and inner_data.get("code") == 200:
                    successfully_cancelled_ids.add(original_order_id)
                    # 更新OMS状态
                    self.oms.update_order_status(original_order_id, 'cancelled')
                else:
                    self.logger.warning(f"⚠️ [NINE CEX] 订单 {original_order_id} 撤单状态异常")
                    # 检查是否是"订单不存在"的错误，如果是则同步OMS状态
                    error_msg = str(inner_data.get("msg", "")).lower()
                    if "not exist" in error_msg or "not found" in error_msg:
                        self.logger.info(f"🔄 [OMS同步] 订单 {original_order_id} 在Nine CEX上不存在，同步OMS状态为cancelled")
                        self.oms.update_order_status(original_order_id, 'cancelled')
            else:
                self.logger.warning(f"⚠️ [NINE CEX] 撤单响应格式异常")

        failed_count = len(order_ids_to_cancel) - len(successfully_cancelled_ids)
        
        if failed_count > 0:
            self.logger.warning(f"📊 [NINE CEX] 撤单部分失败: {len(successfully_cancelled_ids)} 成功, {failed_count} 失败")
            return False  # 有失败的订单，返回False
        else:
            self.logger.info(f"📊 [NINE CEX] 撤单全部成功: {len(successfully_cancelled_ids)} 个")
            return True  # 全部成功才返回True