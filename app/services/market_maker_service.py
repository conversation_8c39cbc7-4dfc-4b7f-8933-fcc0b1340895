# app/services/trading_bot_service.py

from .nine_client import Nine<PERSON><PERSON>
from .binance_client import BinanceClient
from .order_executor import OrderExecutor
from .order_status_monitor import OrderStatusMonitor
from .order_management_service import get_oms, generate_stable_strategy_id
from app.strategies.base_strategy import BaseStrategy

import time
import threading
import logging
from typing import List, Dict, Optional, Any
from decimal import Decimal


class TradingBotService:
    """交易机器人服务 - 核心控制器，协调各个服务组件"""
    
    def __init__(self, nine_client: NineClient, strategy: BaseStrategy, config: dict, 
                 logger: logging.Logger, binance_client: Optional[BinanceClient] = None):
        """
        初始化交易机器人服务
        
        Args:
            nine_client: Nine CEX客户端
            strategy: 交易策略实例
            config: 应用级别的配置
            logger: 日志记录器
            binance_client: 可选的Binance客户端
        """
        self.nine_client = nine_client
        self.strategy = strategy
        self.config = config
        self.logger = logger
        self.binance_client = binance_client

        self.is_running = False
        self.thread: Optional[threading.Thread] = None
        self.dry_run_mode = False
        
        # 初始化服务组件
        self.order_executor = OrderExecutor(nine_client, strategy, config, logger)
        self.order_monitor = OrderStatusMonitor(nine_client, strategy, logger)
        self.oms = get_oms()
        
        # 生成策略ID
        self.strategy_id = generate_stable_strategy_id(strategy)
        
        # 将strategy_id和OMS设置到策略对象上，供策略内部使用
        strategy.strategy_id = self.strategy_id
        strategy.order_management_service = self.oms
        
        # 策略显示名称映射
        strategy_name_map = {
            "MirrorBinanceStrategy": "镜像币安策略",
            "VolumeKlineStrategy": "成交量K线策略", 
            "CumulativeDepthStrategy": "累积深度策略",
            "CrossExchangeArbitrageStrategy": "跨交易所套利策略",
            "LiquidityProviderStrategy": "流动性提供策略",
            "EnhancedLiquidityProviderStrategy": "增强型流动性提供策略",
            "GradientLiquidityStrategy": "梯度流动性支撑策略",
            "AdaptiveKlineStrategy": "自适应K线操盘策略",
            "RaydiumPriceBalanceStrategy": "Raydium价格平衡策略"
        }
        strategy_display_name = strategy_name_map.get(strategy.__class__.__name__, strategy.__class__.__name__)
        self.logger.info(f"交易机器人初始化完成 | 策略: {strategy_display_name}")
        
        # 注册策略到OMS
        self._register_strategy_to_oms(strategy_display_name)
        
        # 验证策略配置
        self._validate_strategy_config(strategy_display_name)
    
    def _register_strategy_to_oms(self, strategy_display_name: str):
        """将策略注册到OMS"""
        strategy_type = self._get_strategy_type(self.strategy.__class__.__name__)
        strategy_config = {
            'class_name': self.strategy.__class__.__name__,
            'display_name': strategy_display_name,
            'trading_pair': getattr(self.strategy, 'trading_pair', 'Unknown'),
            'initialized_at': time.time()
        }
        
        if self.oms.register_strategy(self.strategy_id, strategy_type, strategy_config):
            self.logger.info(f"📋 策略已注册到OMS: {self.strategy_id} ({strategy_type})")
        else:
            self.logger.warning(f"⚠️ 策略注册OMS失败: {self.strategy_id}")
    
    def _get_strategy_type(self, class_name: str) -> str:
        """将策略类名映射为策略类型代码"""
        strategy_type_map = {
            "GradientLiquidityStrategy": "GL",
            "AdaptiveKlineStrategy": "AK", 
            "LiquidityProviderStrategy": "LP",
            "EnhancedLiquidityProviderStrategy": "ELP",
            "MirrorBinanceStrategy": "MB",
            "VolumeKlineStrategy": "VK",
            "CumulativeDepthStrategy": "CDS",
            "CrossExchangeArbitrageStrategy": "ARB",
            "RaydiumPriceBalanceStrategy": "RPB",
            "RaydiumSimpleMarketMaker": "RSM"
        }
        return strategy_type_map.get(class_name, "UNK")
    
    def _validate_strategy_config(self, strategy_display_name: str):
        """验证策略配置"""
        if (self.strategy.strategy_name != "CrossExchangeArbitrageStrategy" and 
            self.strategy.__class__.__name__ != "RaydiumPriceBalanceStrategy"):
            if not self.strategy.api_key or not self.strategy.api_secret:
                self.logger.error(f"策略 '{strategy_display_name}' 缺少Nine CexAPI密钥")
                raise ValueError(f"策略 '{strategy_display_name}' 必须配置Nine CexAPI密钥")
        elif self.strategy.__class__.__name__ == "RaydiumPriceBalanceStrategy":
            self.logger.info(f"🔑 [RaydiumPriceBalanceStrategy] 跳过API密钥检查，策略已自行验证")
        elif self.strategy.strategy_name == "CrossExchangeArbitrageStrategy":
            if not hasattr(self.strategy, 'nine_api_key') or not self.strategy.nine_api_key:
                 self.logger.warning(f"套利策略可能缺少Nine CexAPI密钥，请确保配置 ARB_NINE_API_KEY")
    
    def run_once(self, dry_run: bool = False) -> None:
        """
        执行一次策略循环
        
        Args:
            dry_run: 是否为模拟模式
        """
        try:
            self.dry_run_mode = dry_run
            run_mode = "模拟模式" if dry_run else "实盘模式"
            self.logger.info(f"🚀 开始执行交易策略 | {run_mode}")
            
            # 特殊处理：流动性提供策略需要监控新交易对
            if self.strategy.__class__.__name__ == "LiquidityProviderStrategy":
                self._monitor_new_trading_pairs_for_liquidity_strategy()
            
            # 检查已有订单的成交情况
            self._check_current_orders()
            
            # 获取当前活跃订单
            current_active_orders = self.order_monitor.get_real_active_orders()
            
            self.logger.info(f"🔍 [数据传递] 传递给策略的订单数: {len(current_active_orders)}")
            
            # 订单数量保护 - 但允许策略清理订单
            if len(current_active_orders) >= 100:
                self.logger.warning(f"⚠️ 当前订单数量过多 ({len(current_active_orders)})，允许策略清理但限制新增")
                # 继续执行，但策略应该以清理为主
            
            # 为策略获取Nine CEX订单簿（如果需要）
            nine_cex_book_for_strategy = self._get_order_book_for_strategy()
            
            # 调用策略生成订单
            self.logger.info(f"🤖 策略执行开始...")
            # 根据策略类型调用相应的方法
            if hasattr(self.strategy, 'generate_trades'):
                strategy_decisions = self.strategy.generate_trades(current_active_orders)
            elif hasattr(self.strategy, 'get_actions'):
                strategy_decisions = self.strategy.get_actions(current_active_orders, nine_cex_book_for_strategy)
            else:
                self.logger.error(f"❌ 策略 {self.strategy.__class__.__name__} 缺少generate_trades或get_actions方法")
                return
            
            if not strategy_decisions:
                self.logger.info("📭 策略未生成任何交易决策")
                return
            
            # 处理策略决策
            self._process_strategy_decisions(strategy_decisions, dry_run)
            
        except Exception as e:
            self.logger.error(f"❌ 策略执行异常: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _check_current_orders(self):
        """检查当前订单状态"""
        try:
            # 从OMS获取当前活跃订单ID列表
            oms_orders = self.oms.get_strategy_orders(
                self.strategy_id, 
                status_filter=['pending', 'active']
            )
            
            if oms_orders:
                order_ids = [order.order_id for order in oms_orders]
                self.order_monitor.check_trade_execution(order_ids)
                
        except Exception as e:
            self.logger.error(f"❌ 检查订单状态失败: {e}")
    
    def _process_strategy_decisions(self, strategy_decisions, dry_run: bool):
        """处理策略决策（支持Dict和List两种格式）"""
        orders_to_cancel = []
        orders_to_place = []
        arbitrage_actions = []
        
        # 判断数据格式并解析
        if isinstance(strategy_decisions, dict):
            # Dict格式：{'cancel': [...], 'place': [...]}
            orders_to_cancel = strategy_decisions.get('cancel', [])
            orders_to_place = strategy_decisions.get('place', [])
            arbitrage_actions = strategy_decisions.get('arbitrage', [])
        elif isinstance(strategy_decisions, list):
            # List格式：[{action_type: ..., ...}, ...]
            self.logger.debug(f"🔍 处理{len(strategy_decisions)}个action")
            for i, action in enumerate(strategy_decisions):
                if not isinstance(action, dict):
                    continue
                    
                action_type = action.get('action_type')
                self.logger.debug(f"🔍 Action {i+1}: type={action_type}")
                
                if action_type == 'cancel':
                    orders_to_cancel.extend(action.get('order_ids', []))
                elif action_type == 'place':
                    # 支持两种字段名：trade_beans（旧格式）和orders（新格式）
                    trade_beans = action.get('trade_beans', []) or action.get('orders', [])
                    orders_to_place.extend(trade_beans)
                    self.logger.debug(f"🔍 Place action添加了{len(trade_beans)}个订单")
                elif action_type == 'arbitrage':
                    arbitrage_actions.append(action)
        else:
            self.logger.error(f"❌ 未知的策略决策格式: {type(strategy_decisions)}")
            return
        
        # 处理撤单
        cancel_success = True
        if orders_to_cancel:
            self.logger.info(f"🚫 策略要求撤销 {len(orders_to_cancel)} 个订单")
            cancel_success = self.order_executor.execute_cancellations(orders_to_cancel, dry_run)
            
            if not cancel_success:
                self.logger.error(f"❌ 撤单失败，跳过新订单创建以避免订单累积")
                return  # 撤单失败时直接返回，不创建新订单
        
        # 处理下单 - 只有撤单成功或没有撤单时才执行
        if orders_to_place and cancel_success:
            self.logger.info(f"📝 策略要求下达 {len(orders_to_place)} 个订单")
            self.order_executor.execute_placements(orders_to_place, dry_run)
        elif orders_to_place and not cancel_success:
            self.logger.warning(f"⚠️ 由于撤单失败，跳过 {len(orders_to_place)} 个新订单的创建")
        
        # 处理套利操作（如果有）
        if arbitrage_actions:
            for action in arbitrage_actions:
                self._execute_arbitrage_action(action)
    
    def _execute_arbitrage_action(self, action: Dict[str, Any]):
        """执行套利操作"""
        # 这里保留套利相关的逻辑，因为比较特殊
        try:
            if action.get('platform') == 'binance':
                self._execute_arbitrage_binance_placement(action)
            else:
                self.logger.warning(f"⚠️ 不支持的套利平台: {action.get('platform')}")
        except Exception as e:
            self.logger.error(f"❌ 套利操作失败: {e}")
    
    def _execute_arbitrage_binance_placement(self, action: Dict[str, Any]):
        """执行Binance套利下单"""
        if not self.binance_client:
            self.logger.error("❌ Binance客户端未初始化，无法执行套利操作")
            return
        
        try:
            side = action.get('side', 'BUY')
            symbol = action.get('symbol', 'BTCUSDT')
            quantity = action.get('quantity', '0.001')
            
            self.logger.info(f"🔄 [套利] Binance {side} {quantity} {symbol}")
            
            # 这里调用Binance API
            # result = self.binance_client.place_order(symbol, side, quantity)
            self.logger.info(f"✅ [套利] Binance订单已提交")
            
        except Exception as e:
            self.logger.error(f"❌ [套利] Binance下单失败: {e}")
    
    def start(self) -> None:
        """启动交易机器人"""
        if self.is_running:
            self.logger.warning("⚠️ 交易机器人已在运行中")
            return
        
        self.is_running = True
        self.thread = threading.Thread(target=self._continuous_run, daemon=True)
        self.thread.start()
        self.logger.info("🚀 交易机器人已启动")
    
    def stop(self) -> None:
        """停止交易机器人"""
        if not self.is_running:
            self.logger.warning("⚠️ 交易机器人未在运行")
            return
        
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=5)
        self.logger.info("🛑 交易机器人已停止")
    
    def _continuous_run(self) -> None:
        """持续运行循环"""
        try:
            while self.is_running:
                try:
                    self.run_once(dry_run=self.dry_run_mode)
                    
                    # 获取策略的更新间隔
                    update_interval = getattr(self.strategy, 'update_interval', 60)
                    time.sleep(update_interval)
                    
                except KeyboardInterrupt:
                    self.logger.info("接收到停止信号")
                    break
                except Exception as e:
                    self.logger.error(f"❌ 运行循环异常: {e}")
                    time.sleep(30)  # 出错后等待30秒再重试
                    
        except Exception as e:
            self.logger.error(f"❌ 持续运行异常: {e}")
        finally:
            self.is_running = False
            
    def _monitor_new_trading_pairs_for_liquidity_strategy(self) -> List[str]:
        """
        为流动性提供策略监控新交易对上线
        仅在策略为LiquidityProviderStrategy时调用
        """
        if self.strategy.__class__.__name__ != "LiquidityProviderStrategy":
            return []
        
        try:
            # 获取所有外盘交易对信息
            pool_data = self.nine_client.get_outer_pool_info_all(
                user_api_key=self.strategy.api_key,
                user_api_secret=self.strategy.api_secret,
                page_num=1,
                page_size=100
            )
            
            if not pool_data or 'list' not in pool_data:
                self.logger.warning("⚠️ [NINE CEX] 获取外盘交易对信息失败或为空")
                return []
            
            trading_pairs_list = pool_data.get('list', [])
            current_pairs = set()
            
            # 解析交易对
            for pair_info in trading_pairs_list:
                symbol = pair_info.get('s')  # 交易对符号，如 "SEPBTC"
                if symbol:
                    # 构造完整的交易对名称，格式：{symbol}/USDT
                    full_pair_name = f"{symbol}/USDT"
                    current_pairs.add(full_pair_name)
            
            # 检查是否有新交易对
            new_pairs = current_pairs - self.strategy.known_trading_pairs
            
            if new_pairs:
                self.logger.info(f"🆕 [流动性策略] 发现新交易对: {list(new_pairs)}")
                # 更新已知交易对列表
                self.strategy.known_trading_pairs.update(new_pairs)
                
                # 保存到文件
                self.strategy._save_known_trading_pairs()
                
                # 为每个新交易对初始化配置
                for pair in new_pairs:
                    self.strategy._initialize_pair_config(pair)
                    
                    # 这里可以添加获取初始余额的逻辑
                    # 暂时使用默认值，实际应该从账户API获取
                    self._fetch_initial_balance_for_pair(pair)
                
                return list(new_pairs)
            else:
                # 静默更新已知交易对（防止遗漏）
                if current_pairs != self.strategy.known_trading_pairs:
                    self.strategy.known_trading_pairs.update(current_pairs)
                    self.strategy._save_known_trading_pairs()
                
                self.logger.info(f"📊 [流动性策略] 当前交易对数量: {len(current_pairs)}, 无新增")
                return []
            
        except Exception as e:
            self.logger.error(f"❌ [流动性策略] 监控新交易对失败: {e}")
            return []

    def _fetch_initial_balance_for_pair(self, pair_symbol: str):
        """
        获取新交易对的初始币种余额
        实际实现需要调用账户余额API
        """
        try:
            # 这里需要实现获取账户余额的逻辑
            # 暂时使用模拟数据
            # 实际应该调用类似 get_account_balance API
            
            # 解析币种名称（如 SEPBTC/USDT -> SEPBTC）
            # 使用标准化器避免重复代码
            from app.services.trading_pair_normalizer import TradingPairNormalizer
            normalizer = TradingPairNormalizer()
            normalized = normalizer.normalize(pair_symbol)
            base_currency = normalized.base if normalized else pair_symbol
            
            # 模拟15%的币种余额
            simulated_balance = Decimal("100")  # 假设有100个币
            
            # 更新策略中的余额信息
            if hasattr(self.strategy, 'update_pair_balance'):
                self.strategy.update_pair_balance(pair_symbol, simulated_balance)
                self.logger.info(f"📊 [流动性策略] 设置 {pair_symbol} 初始余额: {simulated_balance} {base_currency}")
            
        except Exception as e:
            self.logger.error(f"❌ [流动性策略] 获取 {pair_symbol} 初始余额失败: {e}")

    def initialize_liquidity_strategy_baseline(self, force: bool = False) -> int:
        """
        为流动性提供策略初始化交易对基线
        将当前所有交易对设置为已知交易对，避免把现有交易对当作新增
        
        Args:
            force: 是否强制重新初始化
            
        Returns:
            int: 初始化的交易对数量
        """
        if self.strategy.__class__.__name__ != "LiquidityProviderStrategy":
            self.logger.error("❌ 此方法仅适用于LiquidityProviderStrategy")
            return 0
        
        try:
            self.logger.info(f"🔧 [流动性策略] 开始初始化交易对基线...")
            
            # 获取当前所有交易对
            pool_data = self.nine_client.get_outer_pool_info_all(
                user_api_key=self.strategy.api_key,
                user_api_secret=self.strategy.api_secret,
                page_num=1,
                page_size=100
            )
            
            if not pool_data or 'list' not in pool_data:
                self.logger.error("❌ [流动性策略] 无法获取交易对信息")
                return 0
            
            trading_pairs_list = pool_data.get('list', [])
            current_pairs = set()
            
            # 解析所有交易对
            for pair_info in trading_pairs_list:
                symbol = pair_info.get('s')
                if symbol:
                    full_pair_name = f"{symbol}/USDT"
                    current_pairs.add(full_pair_name)
            
            if not current_pairs:
                self.logger.warning("⚠️ [流动性策略] 未发现任何交易对")
                return 0
            
            # 检查是否需要初始化
            if not force and self.strategy.known_trading_pairs:
                existing_count = len(self.strategy.known_trading_pairs)
                self.logger.warning(f"⚠️ [流动性策略] 已存在 {existing_count} 个已知交易对")
                self.logger.info(f"当前已知: {list(self.strategy.known_trading_pairs)}")
                self.logger.info(f"如需重新初始化，请设置 force=True")
                return existing_count
            
            # 设置基线
            self.strategy.known_trading_pairs = current_pairs.copy()
            self.strategy._save_known_trading_pairs()
            
            initialized_count = len(current_pairs)
            self.logger.info(f"✅ [流动性策略] 基线初始化完成: {initialized_count} 个交易对")
            self.logger.info(f"基线交易对: {list(current_pairs)}")
            
            return initialized_count
            
        except Exception as e:
            self.logger.error(f"❌ [流动性策略] 基线初始化失败: {e}")
            return 0

    def _get_order_book_for_strategy(self) -> Optional[Dict]:
        """为策略获取Nine CEX订单簿（如果需要）"""
        if not (hasattr(self.strategy, 'fetch_nine_book_symbol') and 
                hasattr(self.strategy, 'fetch_nine_book_precision') and 
                hasattr(self.strategy, 'fetch_nine_book_depth')):
            return None
        
        symbol = self.strategy.fetch_nine_book_symbol
        precision = self.strategy.fetch_nine_book_precision
        # depth = self.strategy.fetch_nine_book_depth  # 当前未使用
        
        try:
            # 使用策略自身的 API 密钥
            if not self.strategy.api_key or not self.strategy.api_secret:
                self.logger.error(f"策略 {self.strategy.strategy_name} 需要API密钥获取订单簿，但未设置")
                return None
            
            # 尝试解析交易对ID以支持新的API格式
            trade_pair_id = None
            try:
                trade_pair_id = self.strategy.resolve_trading_pair_id(symbol)
            except Exception:
                trade_pair_id = symbol  # 回退到原始symbol
            
            # 备用方案：如果策略有配置备用交易对ID，使用配置中的值
            if trade_pair_id == symbol and hasattr(self.strategy, '_get_config_value'):
                backup_id = None
                try:
                    # 尝试从策略配置中获取备用交易对ID
                    if symbol == self.strategy._get_config_value('RPB_TRADING_PAIR'):
                        backup_id = self.strategy._get_config_value('RPB_TRADING_PAIR_ID')
                except:
                    pass
                
                if backup_id:
                    trade_pair_id = backup_id
                    self.logger.info(f"使用备用交易对ID: {symbol} -> {trade_pair_id}")
            
            # 获取订单簿数据
            nine_cex_book = self.nine_client.get_outer_pool_latest_order(
                trade_pair_name=symbol,
                api_key=self.strategy.api_key,
                secret=self.strategy.api_secret,
                trade_pair_id=trade_pair_id,
                precision=precision
            )
            
            if nine_cex_book and "orderDepth" in nine_cex_book:
                return nine_cex_book
            else:
                self.logger.warning(f"订单簿数据异常: {symbol}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取订单簿失败 {symbol}: {e}")
            return None

    def get_status(self) -> Dict[str, Any]:
        """获取交易机器人状态"""
        return {
            'is_running': self.is_running,
            'strategy_name': self.strategy.__class__.__name__,
            'strategy_id': self.strategy_id,
            'dry_run_mode': self.dry_run_mode,
            'thread_alive': self.thread.is_alive() if self.thread else False
        }


# =============================================================================
# CLI Commands
# =============================================================================

import click
from flask import current_app
from flask.cli import with_appcontext


@click.command("start-trading-bot")
@click.option('--strategy', 
              type=click.Choice(['gradient_liquidity', 'adaptive_kline', 'mirror_binance', 'mirror_price', 'liquidity_provider', 'enhanced_liquidity_provider', 'volume_kline', 'cumulative_depth', 'cross_exchange_arbitrage', 'raydium_price_balance', 'cex_price_balance'], case_sensitive=False),
              default=None,
              help='Specify the trading strategy to run, overriding .env settings.')
@click.option('--dry-run', is_flag=True, help='Simulate bot actions without executing them.')
@with_appcontext
def start_trading_bot_command(strategy: Optional[str], dry_run: bool):
    """启动后台交易机器人。"""
    app = current_app
    # Use a key for extensions dictionary to store/retrieve the bot service
    bot_service_key = 'trading_bot_service_instance'

    if hasattr(app, 'extensions') and bot_service_key in app.extensions and app.extensions[bot_service_key].is_running:
        app.logger.info("CLI: 交易机器人已在运行中")
        click.echo("交易机器人已在运行中")
        return

    try:
        # Dynamically import setup_trading_bot from app module to avoid circular dependency at module level
        from app import setup_trading_bot as global_setup_trading_bot
        # Pass the current app instance and the strategy override to the setup function from __init__.py
        bot_service_instance = global_setup_trading_bot(app, strategy_override=strategy)

        # 检查bot_service_instance是否创建成功
        if bot_service_instance is None:
            app.logger.error("CLI: 机器人服务初始化失败，请检查配置")
            click.echo("机器人服务初始化失败，请检查配置（如API密钥等）", err=True)
            return

        # 设置模拟运行模式
        if dry_run:
            bot_service_instance.dry_run_mode = True
            app.logger.info("CLI: 启用模拟运行模式")
        
        # Store the bot service instance in app.extensions for access by 'stop' command or status checks
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        app.extensions[bot_service_key] = bot_service_instance
        
        bot_service_instance.start()
        strategy_name_map = {
            "MirrorBinanceStrategy": "镜像币安策略",
            "VolumeKlineStrategy": "成交量K线策略", 
            "CumulativeDepthStrategy": "累积深度策略",
            "CrossExchangeArbitrageStrategy": "跨交易所套利策略",
            "LiquidityProviderStrategy": "流动性提供策略",
            "EnhancedLiquidityProviderStrategy": "增强型流动性提供策略",
            "GradientLiquidityStrategy": "梯度流动性支撑策略",
            "AdaptiveKlineStrategy": "自适应K线操盘策略"
        }
        strategy_display_name = strategy_name_map.get(bot_service_instance.strategy.__class__.__name__, bot_service_instance.strategy.__class__.__name__)
        app.logger.info(f"CLI: 交易机器人启动成功 | 策略: {strategy_display_name}")
        click.echo(f"交易机器人启动成功 | 策略: {strategy_display_name} | 按 Ctrl+C 停止")
        
        # Keep the main thread alive and responsive to Ctrl+C
        try:
            while bot_service_instance.is_running:
                time.sleep(1) # Check every second
        except KeyboardInterrupt:
            app.logger.info("CLI: 收到停止信号，正在关闭机器人...")
            click.echo("\n收到停止信号，正在关闭机器人...")
            bot_service_instance.stop()
            # 等待机器人实际停止
            if bot_service_instance.thread and bot_service_instance.thread.is_alive():
                bot_service_instance.thread.join()
            app.logger.info("CLI: 机器人已通过键盘中断停止")
            click.echo("机器人已停止")

    except ValueError as e:
        app.logger.error(f"CLI: 交易机器人设置错误: {e}")
        click.echo(f"交易机器人设置错误: {e}", err=True)
    except Exception as e:
        app.logger.error(f"CLI: 启动交易机器人时发生异常: {e}")
        click.echo(f"发生异常: {e}", err=True)


@click.command("stop-trading-bot")
@with_appcontext
def stop_trading_bot_command():
    """停止后台交易机器人。"""
    app = current_app
    bot_service_key = 'trading_bot_service_instance'

    if hasattr(app, 'extensions') and bot_service_key in app.extensions:
        bot_service_instance = app.extensions[bot_service_key]
        if bot_service_instance.is_running:
            bot_service_instance.stop()
            app.logger.info("CLI: 交易机器人停止命令已发出")
            click.echo("交易机器人停止命令已发出，请查看日志确认关闭状态")
        else:
            app.logger.info("CLI: 交易机器人未在运行")
            click.echo("交易机器人未在运行")
    else:
        app.logger.info("CLI: 未找到交易机器人服务实例，是否已启动?")
        click.echo("未找到交易机器人服务实例，请使用 'flask start-trading-bot' 启动")


@click.command("run-bot-once")
@click.option('--strategy', 
              type=click.Choice(['gradient_liquidity', 'adaptive_kline', 'mirror_binance', 'mirror_price', 'liquidity_provider', 'enhanced_liquidity_provider', 'volume_kline', 'cumulative_depth', 'cross_exchange_arbitrage', 'raydium_price_balance', 'cex_price_balance'], case_sensitive=False),
              default=None, # Default to None, logic in setup will handle it
              help='Specify the trading strategy to run, overriding .env settings.')
@click.option('--dry-run', is_flag=True, help='Simulate bot actions without executing them.')
@with_appcontext
def run_bot_once_command(strategy: Optional[str], dry_run: bool):
    """执行一次机器人交易逻辑。"""
    app = current_app
    try:
        # Dynamically import setup_trading_bot from app module
        from app import setup_trading_bot as global_setup_trading_bot
        
        # Setup the bot service for a single run. This will create a new instance.
        # We don't store this in app.extensions as it's for a single execution.
        bot_service_instance = global_setup_trading_bot(app, strategy_override=strategy)
        
        # 检查bot_service_instance是否创建成功
        if bot_service_instance is None:
            app.logger.error("CLI: 机器人服务初始化失败，请检查配置")
            click.echo("机器人服务初始化失败，请检查配置（如API密钥等）", err=True)
            return
        
        strategy_name_map = {
            "MirrorBinanceStrategy": "镜像币安策略",
            "VolumeKlineStrategy": "成交量K线策略", 
            "CumulativeDepthStrategy": "累积深度策略",
            "CrossExchangeArbitrageStrategy": "跨交易所套利策略",
            "LiquidityProviderStrategy": "流动性提供策略",
            "EnhancedLiquidityProviderStrategy": "增强型流动性提供策略",
            "GradientLiquidityStrategy": "梯度流动性支撑策略",
            "AdaptiveKlineStrategy": "自适应K线操盘策略"
        }
        strategy_display_name = strategy_name_map.get(bot_service_instance.strategy.__class__.__name__, bot_service_instance.strategy.__class__.__name__)
        run_mode = "模拟模式" if dry_run else "实盘模式"
        app.logger.info(f"CLI: 执行单次运行 | 策略: {strategy_display_name} | {run_mode}")
        click.echo(f"执行单次运行 | 策略: {strategy_display_name} | {run_mode}")
        
        bot_service_instance.run_once(dry_run=dry_run)
        
        app.logger.info("CLI: 单次运行完成")
        click.echo("单次运行完成，请查看日志了解详情")

    except ValueError as e:
        app.logger.error(f"CLI: 交易机器人设置错误: {e}")
        click.echo(f"交易机器人设置错误: {e}", err=True)
    except Exception as e:
        app.logger.error(f"CLI: 单次运行时发生异常: {e}")
        click.echo(f"单次运行时发生异常: {e}", err=True)


@click.command("init-liquidity-baseline")
@click.option('--force', is_flag=True, help='强制重新初始化，覆盖现有数据')
@with_appcontext
def init_liquidity_baseline_command(force: bool):
    """初始化流动性提供策略的交易对基线。"""
    app = current_app
    try:
        # 导入setup_trading_bot
        from app import setup_trading_bot as global_setup_trading_bot
        
        # 创建流动性策略实例
        bot_service_instance = global_setup_trading_bot(app, strategy_override='liquidity_provider')
        
        # 检查bot_service_instance是否创建成功
        if bot_service_instance is None:
            app.logger.error("CLI: 机器人服务初始化失败，请检查配置")
            click.echo("机器人服务初始化失败，请检查配置（如API密钥等）", err=True)
            return
        
        if bot_service_instance.strategy.__class__.__name__ != "LiquidityProviderStrategy":
            app.logger.error("CLI: 此命令仅适用于流动性提供策略")
            click.echo("此命令仅适用于流动性提供策略", err=True)
            return
        
        app.logger.info("CLI: 开始初始化流动性策略交易对基线")
        click.echo("🔧 开始初始化流动性策略交易对基线...")
        
        # 初始化基线
        initialized_count = bot_service_instance.initialize_liquidity_strategy_baseline(force=force)
        
        if initialized_count > 0:
            app.logger.info(f"CLI: 基线初始化成功，共{initialized_count}个交易对")
            click.echo(f"✅ 基线初始化成功！共设置 {initialized_count} 个已知交易对")
            click.echo("现在运行策略时，只有新增的交易对才会被识别为新交易对")
        
    except ValueError as e:
        app.logger.error(f"CLI: 交易机器人设置错误: {e}")
        click.echo(f"交易机器人设置错误: {e}", err=True)
    except Exception as e:
        app.logger.error(f"CLI: 初始化时发生异常: {e}")
        click.echo(f"初始化时发生异常: {e}", err=True)


@click.command("clean-strategy-orders")
@click.option('--strategy', 
              type=click.Choice(['gradient_liquidity', 'adaptive_kline', 'mirror_binance', 'mirror_price', 'liquidity_provider', 'enhanced_liquidity_provider', 'volume_kline', 'cumulative_depth', 'cross_exchange_arbitrage', 'raydium_price_balance', 'cex_price_balance'], case_sensitive=False),
              required=False,
              help='指定要清理订单的策略类型')
@click.option('--all-strategies', is_flag=True, help='清理所有策略的订单')
@click.option('--trading-pair',
              type=str,
              help='指定交易对（可选，如SOL/USDT）。不指定则清理该策略的所有订单')
@click.option('--local-only', is_flag=True, help='仅清理本地OMS缓存，不撤销CEX上的实际订单')
@click.option('--cex-only', is_flag=True, help='仅撤销CEX上的订单，不清理本地缓存')
@click.option('--all', is_flag=True, help='同时清理本地OMS缓存和撤销CEX上的订单（默认行为）')
@click.option('--all-cex-orders', is_flag=True, help='撤销CEX上所有指定交易对的订单，不限策略（需要配合--trading-pair使用）')
@click.option('--dry-run', is_flag=True, help='预览模式，显示将要清理的订单但不执行')
@click.option('--force', is_flag=True, help='强制清理，跳过确认')
@click.option('--yes', 'yes_flag', is_flag=True, help='自动确认所有操作，跳过确认对话')
@with_appcontext
def clean_strategy_orders_command(strategy: str, all_strategies: bool, trading_pair: str, local_only: bool, cex_only: bool, all: bool, all_cex_orders: bool, dry_run: bool, force: bool, yes_flag: bool):
    """清理特定策略的订单，包括本地OMS缓存和CEX上的实际订单。
    
    这个命令用于清理策略异常退出后残留的订单：
    - 本地OMS缓存中的订单记录
    - CEX交易所上的实际挂单订单
    
    示例：
    flask clean-strategy-orders --strategy cex_price_balance
    flask clean-strategy-orders --all-strategies
    flask clean-strategy-orders --strategy cex_price_balance --trading-pair SOL/USDT
    flask clean-strategy-orders --all-strategies --dry-run
    flask clean-strategy-orders --strategy cex_price_balance --local-only
    flask clean-strategy-orders --all-cex-orders --trading-pair SOL/USDT  # 清理CEX上所有SOL/USDT订单
    """
    app = current_app
    
    # 合并force和yes标志
    skip_confirmation = force or yes_flag
    
    # 验证参数冲突
    conflicting_options = sum([local_only, cex_only, all, all_cex_orders])
    if conflicting_options > 1:
        click.echo("❌ 错误：--local-only、--cex-only、--all 和 --all-cex-orders 不能同时使用", err=True)
        return
    
    # 处理 --all-cex-orders 特殊情况
    if all_cex_orders:
        if not trading_pair:
            click.echo("❌ 错误：--all-cex-orders 必须配合 --trading-pair 使用", err=True)
            return
        # 清理CEX上所有指定交易对的订单
        _clean_all_cex_orders_for_pair(app, trading_pair, dry_run, skip_confirmation)
        return
    
    # 验证策略选择参数
    if not strategy and not all_strategies:
        click.echo("❌ 错误：必须指定 --strategy 或 --all-strategies 中的一个", err=True)
        return
    
    if strategy and all_strategies:
        click.echo("❌ 错误：--strategy 和 --all-strategies 不能同时使用", err=True)
        return
    
    # 如果没有指定任何清理选项，默认为全部清理
    if not local_only and not cex_only and not all:
        all = True
    
    try:
        # 导入必要的服务
        from app import setup_trading_bot as global_setup_trading_bot
        from .order_management_service import get_oms, generate_stable_strategy_id
        
        if all_strategies:
            app.logger.info(f"CLI: 开始清理所有策略订单 - 交易对: {trading_pair or 'ALL'}")
            click.echo("🔧 正在清理所有策略的订单...")
            _clean_all_strategies_orders(app, trading_pair, local_only, cex_only, all, dry_run, skip_confirmation)
            return
        else:
            app.logger.info(f"CLI: 开始清理策略订单 - 策略: {strategy}, 交易对: {trading_pair or 'ALL'}")
            
            # 创建策略实例以获取策略ID和配置
            bot_service_instance = global_setup_trading_bot(app, strategy_override=strategy)
            
            if bot_service_instance is None:
                click.echo("❌ 策略初始化失败，请检查配置", err=True)
                return
            
            strategy_instance = bot_service_instance.strategy
            strategy_id = generate_stable_strategy_id(strategy_instance)
            
            # 简化信息显示
            trading_pair_info = f" | {strategy_instance.trading_pair}" if hasattr(strategy_instance, 'trading_pair') else ""
            click.echo(f"🔧 清理策略: {strategy_instance.__class__.__name__}{trading_pair_info}")
            app.logger.debug(f"策略ID: {strategy_id}")
            
            # 获取OMS服务
            oms = get_oms()
            
            # 清理单个策略
            _clean_single_strategy_orders(app, bot_service_instance, strategy_id, oms, 
                                        trading_pair, local_only, cex_only, all, dry_run, skip_confirmation)
        
    except ValueError as e:
        app.logger.error(f"CLI: 策略订单清理参数错误: {e}")
        click.echo(f"❌ 参数错误: {e}", err=True)
    except Exception as e:
        app.logger.error(f"CLI: 策略订单清理异常: {e}")
        click.echo(f"❌ 清理过程中发生异常: {e}", err=True)


def _clean_all_strategies_orders(app, trading_pair: str, local_only: bool, cex_only: bool, all: bool, dry_run: bool, skip_confirmation: bool):
    """清理所有策略的订单"""
    from .order_management_service import get_oms
    from app import setup_trading_bot as global_setup_trading_bot
    
    # 所有支持的策略列表
    all_strategy_names = [
        'gradient_liquidity', 'adaptive_kline', 'mirror_binance', 'mirror_price', 
        'liquidity_provider', 'enhanced_liquidity_provider', 'volume_kline', 
        'cumulative_depth', 'cross_exchange_arbitrage', 'raydium_price_balance', 'cex_price_balance'
    ]
    
    oms = get_oms()
    total_local_cleaned = 0
    total_cex_cleaned = 0
    
    if local_only or all:
        click.echo("🗃️ 正在扫描所有策略的本地OMS缓存...")
        
        # 获取所有策略的订单
        all_local_orders = []
        for strategy_name in all_strategy_names:
            try:
                bot_service = global_setup_trading_bot(app, strategy_override=strategy_name)
                if bot_service:
                    from .order_management_service import generate_stable_strategy_id
                    strategy_id = generate_stable_strategy_id(bot_service.strategy)
                    local_orders = oms.get_strategy_orders(strategy_id, ['pending', 'active'])
                    
                    if trading_pair:
                        local_orders = [order for order in local_orders if order.trading_pair == trading_pair]
                    
                    all_local_orders.extend([(order, strategy_name) for order in local_orders])
            except Exception as e:
                app.logger.warning(f"跳过策略 {strategy_name}: {e}")
                continue
        
        if all_local_orders:
            click.echo(f"📊 找到 {len(all_local_orders)} 个本地缓存订单")
            
            if dry_run:
                click.echo("🔍 预览模式 - 本地缓存订单：")
                for order, strategy_name in all_local_orders[:5]:
                    click.echo(f"  - {order.order_id} | {strategy_name} | {order.trading_pair} | {order.status}")
                if len(all_local_orders) > 5:
                    click.echo(f"  ... 还有 {len(all_local_orders) - 5} 个订单")
            else:
                if not skip_confirmation:
                    if not click.confirm(f"确认清理 {len(all_local_orders)} 个本地缓存订单？"):
                        click.echo("❌ 用户取消操作")
                        return
                
                # 按策略清理
                for strategy_name in all_strategy_names:
                    try:
                        bot_service = global_setup_trading_bot(app, strategy_override=strategy_name)
                        if bot_service:
                            from .order_management_service import generate_stable_strategy_id
                            strategy_id = generate_stable_strategy_id(bot_service.strategy)
                            cleared_count = oms.clear_strategy_active_orders(strategy_id)
                            if cleared_count > 0:
                                total_local_cleaned += cleared_count
                                click.echo(f"✅ 已清理 {strategy_name} 策略的 {cleared_count} 个本地订单")
                    except Exception as e:
                        app.logger.warning(f"清理策略 {strategy_name} 失败: {e}")
                        continue
                
                click.echo(f"✅ 总计清理 {total_local_cleaned} 个本地缓存订单")
                app.logger.info(f"CLI: 清理所有策略本地缓存订单成功 - {total_local_cleaned}个")
        else:
            click.echo("ℹ️ 本地缓存中未找到任何策略的活跃订单")
    
    if cex_only or all:
        click.echo("🌐 正在扫描所有策略的CEX实际订单...")
        
        # 清理CEX订单需要每个策略的API密钥，所以需要逐个处理
        strategy_results = []
        for strategy_name in all_strategy_names:
            try:
                bot_service = global_setup_trading_bot(app, strategy_override=strategy_name)
                if bot_service:
                    click.echo(f"🔍 检查策略 {strategy_name} 的CEX订单...")
                    cex_cleaned = _clean_strategy_cex_orders(
                        app, bot_service, trading_pair, dry_run, skip_confirmation
                    )
                    total_cex_cleaned += cex_cleaned
                    if cex_cleaned > 0:
                        strategy_results.append(f"{strategy_name}: {cex_cleaned}个")
                        click.echo(f"  ✅ {strategy_name} 清理了 {cex_cleaned} 个CEX订单")
                    else:
                        click.echo(f"  ℹ️ {strategy_name} 无CEX订单需要清理")
            except Exception as e:
                app.logger.warning(f"跳过策略 {strategy_name} 的CEX清理: {e}")
                click.echo(f"  ⚠️ {strategy_name} 跳过: {e}")
                continue
        
        if total_cex_cleaned > 0:
            click.echo(f"✅ 总计撤销 {total_cex_cleaned} 个CEX订单 ({', '.join(strategy_results)})")
        else:
            click.echo("ℹ️ 所有策略均未找到CEX活跃订单")
    
    if dry_run:
        click.echo("🔍 预览模式完成，未执行实际清理操作")
    else:
        click.echo(f"✅ 所有策略订单清理完成 - 本地: {total_local_cleaned}, CEX: {total_cex_cleaned}")


def _clean_all_cex_orders_for_pair(app, trading_pair: str, dry_run: bool, skip_confirmation: bool):
    """清理CEX上所有指定交易对的订单，不限策略"""
    from .nine_client import NineClient
    from .order_executor import OrderExecutor
    import os
    
    try:
        # 创建Nine客户端
        api_key = os.getenv('NINE_API_KEY') or app.config.get('NINE_API_KEY')
        api_secret = os.getenv('NINE_API_SECRET') or app.config.get('NINE_API_SECRET')
        
        if not api_key or not api_secret:
            click.echo("❌ 错误：缺少API凭证，请配置NINE_API_KEY和NINE_API_SECRET", err=True)
            return
        
        nine_client = app.nine_client if hasattr(app, 'nine_client') else None
        if not nine_client:
            click.echo("❌ 错误：无法获取Nine客户端", err=True)
            return
        
        click.echo(f"🔧 清理CEX上所有 {trading_pair} 订单（不限策略）")
        
        # 规范化交易对格式
        normalized_pair = trading_pair.replace('/', '').replace(' ', '').upper()
        
        total_cancelled = 0
        iteration = 0
        max_iterations = 100
        
        while iteration < max_iterations:
            iteration += 1
            
            # 获取所有订单
            result = nine_client.get_current_orders(
                trade_pair=trading_pair,
                user_api_key=api_key,
                user_api_secret=api_secret,
                page_size=500,
                page_num=1,
                get_all_pages=True
            )
            
            if not result or result.get("code") != 200:
                click.echo(f"❌ 获取订单失败: {result}")
                break
            
            orders = result.get("data", {}).get("list", [])
            
            if not orders:
                if iteration == 1:
                    click.echo(f"ℹ️ 未找到 {trading_pair} 的活跃订单")
                break
            
            if iteration == 1:
                click.echo(f"📊 找到 {len(orders)} 个 {trading_pair} 的活跃订单")
                
                if dry_run:
                    click.echo(f"🔍 预览模式：将撤销 {len(orders)} 个订单")
                    for order in orders[:5]:
                        click.echo(f"  - {order.get('orderId', 'N/A')} | 价格: {order.get('orderPrice', 'N/A')} | 数量: {order.get('quantity', 'N/A')}")
                    if len(orders) > 5:
                        click.echo(f"  ... 还有 {len(orders) - 5} 个订单")
                    return
                
                if not skip_confirmation:
                    if not click.confirm(f"确认撤销这 {len(orders)} 个订单？"):
                        click.echo("❌ 用户取消操作")
                        return
            
            # 提取订单ID
            order_ids = [order.get('orderId') for order in orders if order.get('orderId')]
            
            if order_ids:
                click.echo(f"🔄 第{iteration}轮: 撤销 {len(order_ids)} 个订单...")
                
                # 批量撤销
                cancel_result = nine_client.batch_cancel_orders(
                    order_ids=order_ids,
                    user_api_key=api_key,
                    user_api_secret=api_secret
                )
                
                if cancel_result and cancel_result.get("code") == 200:
                    batch_count = len(order_ids)
                    total_cancelled += batch_count
                    click.echo(f"✅ 第{iteration}轮: 成功撤销 {batch_count} 个订单 (累计: {total_cancelled})")
                else:
                    click.echo(f"⚠️ 第{iteration}轮撤单失败: {cancel_result}")
                    break
        
        if total_cancelled > 0:
            click.echo(f"✅ 完成！共撤销 {total_cancelled} 个 {trading_pair} 订单 (共{iteration}批)")
        else:
            click.echo(f"ℹ️ 未撤销任何订单")
            
    except Exception as e:
        click.echo(f"❌ 清理订单时出错: {e}")
        app.logger.error(f"清理所有CEX订单异常: {e}")


def _clean_strategy_cex_orders(app, bot_service, trading_pair: str, dry_run: bool, skip_confirmation: bool) -> int:
    """清理单个策略的CEX订单，返回清理数量"""
    from .order_status_monitor import OrderStatusMonitor
    from .order_executor import OrderExecutor
    
    try:
        monitor = OrderStatusMonitor(bot_service.nine_client, bot_service.strategy, app.logger)
        executor = OrderExecutor(bot_service.nine_client, bot_service.strategy, bot_service.config, app.logger)
        
        total_cancelled = 0
        iteration = 0
        max_iterations = 100  # 防止无限循环
        
        # 首次获取所有订单
        cex_orders = monitor.get_real_active_orders_from_api()
        
        if trading_pair:
            # 规范化交易对格式进行比较
            normalized_pair = trading_pair.replace('/', '').replace(' ', '').upper()
            cex_orders = [order for order in cex_orders 
                         if (order.get('tradePair', '').upper() == normalized_pair or 
                             order.get('symbol', '').upper() == normalized_pair)]
        
        if not cex_orders:
            return 0
        
        initial_count = len(cex_orders)
        click.echo(f"  📊 找到 {initial_count} 个CEX订单")
        
        if dry_run:
            click.echo(f"  🔍 预览模式：将撤销 {initial_count} 个活跃订单")
            for order in cex_orders[:3]:  # 只显示前3个
                click.echo(f"    - {order.get('orderId', 'N/A')} | {order.get('tradePairName', 'N/A')}")
            if len(cex_orders) > 3:
                click.echo(f"    ... 还有 {len(cex_orders) - 3} 个订单")
            return initial_count
        
        # 询问确认（如果不是skip_confirmation模式）
        if not skip_confirmation:
            if not click.confirm(f"  确认撤销这 {initial_count} 个CEX订单？"):
                return 0
        
        # 循环撤销直到没有订单
        while iteration < max_iterations:
            iteration += 1
            
            # 重新获取当前订单（因为之前的可能已经被撤销）
            current_orders = monitor.get_real_active_orders_from_api()
            
            if trading_pair:
                # 规范化交易对格式进行比较
                normalized_pair = trading_pair.replace('/', '').replace(' ', '').upper()
                current_orders = [order for order in current_orders 
                                if (order.get('tradePair', '').upper() == normalized_pair or 
                                    order.get('symbol', '').upper() == normalized_pair)]
            
            if not current_orders:
                # 没有更多订单了
                break
            
            # 撤销当前批次的订单
            order_ids = [order.get('orderId') for order in current_orders if order.get('orderId')]
            
            if order_ids:
                click.echo(f"  🔄 第{iteration}轮: 撤销 {len(order_ids)} 个订单...")
                
                success_result = executor.execute_cancellations(order_ids)
                
                if success_result:
                    batch_count = len(order_ids)
                    total_cancelled += batch_count
                    click.echo(f"  ✅ 第{iteration}轮: 成功撤销 {batch_count} 个订单 (累计: {total_cancelled})")
                else:
                    app.logger.warning(f"  ⚠️ 第{iteration}轮撤单失败")
                    click.echo(f"  ⚠️ 第{iteration}轮撤单失败，停止处理")
                    break
        
        if total_cancelled > 0:
            click.echo(f"  ✅ 撤销完成: 共处理 {total_cancelled} 个CEX订单 (共{iteration}批)")
        else:
            if iteration == 1:
                click.echo("  ℹ️ 未找到需要撤销的订单")
        
        return total_cancelled
            
    except Exception as e:
        click.echo(f"  ❌ 获取CEX订单时出错: {e}")
        app.logger.error(f"CLI: 策略CEX订单清理异常: {e}")
        return 0


def _clean_single_strategy_orders(app, bot_service_instance, strategy_id, oms, 
                                trading_pair: str, local_only: bool, cex_only: bool, all: bool, 
                                dry_run: bool, skip_confirmation: bool):
    """清理单个策略的订单"""
    # 清理本地OMS缓存
    if local_only or all:
        click.echo("🗃️ 正在扫描本地OMS缓存...")
        local_orders = oms.get_strategy_orders(strategy_id, ['pending', 'active'])
        
        if trading_pair:
            # 过滤指定交易对的订单
            local_orders = [order for order in local_orders if order.trading_pair == trading_pair]
        
        if local_orders:
            click.echo(f"📊 找到 {len(local_orders)} 个本地缓存订单")
            
            if dry_run:
                click.echo("🔍 预览模式 - 本地缓存订单：")
                for order in local_orders[:5]:  # 只显示前5个
                    click.echo(f"  - {order.order_id} | {order.trading_pair} | {order.status}")
                if len(local_orders) > 5:
                    click.echo(f"  ... 还有 {len(local_orders) - 5} 个订单")
            else:
                if not skip_confirmation:
                    if not click.confirm(f"确认清理 {len(local_orders)} 个本地缓存订单？"):
                        click.echo("❌ 用户取消操作")
                        return
                
                cleared_count = oms.clear_strategy_active_orders(strategy_id)
                click.echo(f"✅ 已清理 {cleared_count} 个本地缓存订单")
                app.logger.info(f"CLI: 清理本地缓存订单成功 - {cleared_count}个")
        else:
            click.echo("ℹ️ 本地缓存中未找到该策略的活跃订单")
    
    # 清理CEX上的实际订单
    if cex_only or all:
        click.echo("🌐 正在扫描CEX上的实际订单...")
        cex_cleaned = _clean_strategy_cex_orders(app, bot_service_instance, trading_pair, dry_run, skip_confirmation)
        
        if cex_cleaned == 0:
            click.echo("ℹ️ CEX上未找到该策略的活跃订单")
    
    if dry_run:
        click.echo("🔍 预览模式完成，未执行实际清理操作")
    else:
        click.echo("✅ 策略订单清理完成")