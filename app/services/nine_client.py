# app/services/nine_client.py

import requests
import time
import logging
from decimal import Decimal
from typing import Optional, Dict, List, Any

class NineClient:
    def __init__(self, api_key: str, secret: str, api_url: str, wallet_api_url: str = None, 
                 logger: Optional[logging.Logger] = None,
                 pool_connections: int = 20, pool_maxsize: int = 100,
                 max_retries: int = 3, backoff_factor: float = 0.3,
                 connect_timeout: int = 10, read_timeout: int = 30):
        """
        初始化 Nine CEX API 客户端。

        Args:
            api_key (str): Nine CEX API Key。
            secret (str): Nine CEX API Secret。
            api_url (str): Nine CEX API 基础 URL (不包含协议头)。
            logger (Optional[logging.Logger]): 可选的日志记录器。
            pool_connections (int): 连接池数量，默认20。
            pool_maxsize (int): 每个连接池的最大连接数，默认100。
            max_retries (int): 最大重试次数，默认3。
            backoff_factor (float): 重试退避因子，默认0.3。
            connect_timeout (int): 连接超时时间（秒），默认10。
            read_timeout (int): 读取超时时间（秒），默认30。
        """
        self.api_key = api_key
        self.secret = secret
        # Ensure the API URL has a protocol. If not provided, default to https.
        self.api_url = api_url
        if not self.api_url.startswith(('http://', 'https://')):
            self.api_url = f"https://{self.api_url}"
            
        # 钱包API URL (用于余额查询等钱包功能)
        self.wallet_api_url = wallet_api_url or api_url
        if not self.wallet_api_url.startswith(('http://', 'https://')):
            self.wallet_api_url = f"https://{self.wallet_api_url}"

        self.logger = logger or logging.getLogger(__name__)

        # 存储连接配置参数
        self.connect_timeout = connect_timeout
        self.read_timeout = read_timeout

        # 创建持久的requests会话，优化连接池配置
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        self.session = requests.Session()

        # 配置连接池适配器，支持高并发
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],  # 添加429限流状态码
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]  # 新版本使用allowed_methods
        )

        adapter = HTTPAdapter(
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize,
            max_retries=retry_strategy
        )

        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # 设置通用请求头，匹配Nine CEX API期望
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'  # 保持连接复用
        })

        self.logger.debug(f"连接池配置: {pool_connections}/{pool_maxsize}, 重试{max_retries}, 超时{connect_timeout}s/{read_timeout}s")

    @classmethod
    def create_optimized_client(cls, api_key: str, secret: str, api_url: str,
                              logger: Optional[logging.Logger] = None,
                              concurrent_level: int = 99) -> 'NineClient':
        """
        创建针对高并发优化的NineClient实例。

        Args:
            api_key (str): Nine CEX API Key。
            secret (str): Nine CEX API Secret。
            api_url (str): Nine CEX API 基础 URL。
            logger (Optional[logging.Logger]): 可选的日志记录器。
            concurrent_level (int): 预期并发级别，用于优化连接池配置。

        Returns:
            NineClient: 优化配置的客户端实例。
        """
        # 根据并发级别动态调整连接池配置
        pool_connections = min(max(concurrent_level // 5, 10), 50)  # 10-50之间
        pool_maxsize = min(max(concurrent_level + 20, 50), 200)     # 50-200之间

        return cls(
            api_key=api_key,
            secret=secret,
            api_url=api_url,
            logger=logger,
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize,
            max_retries=3,
            backoff_factor=0.1,  # 更快的重试
            connect_timeout=5,   # 更短的连接超时
            read_timeout=20      # 适中的读取超时
        )

    def _request_with_headers(self, endpoint: str, params: dict | None, headers: dict, method: str = "GET", use_wallet_url: bool = False) -> dict | None:
        """
        发送带自定义Headers的HTTP请求到Nine CEX API。
        
        Args:
            endpoint (str): API端点路径 (例如 "/trade/curorder/list")。
            params (dict | None): 查询参数（GET）或请求体（POST）。
            headers (dict): 自定义HTTP头。
            method (str): HTTP方法，默认为"GET"。
            use_wallet_url (bool): 是否使用wallet_api_url，默认False。
            
        Returns:
            dict | None: 响应的JSON数据，或者在出错时返回None。
        """
        # 根据参数选择使用哪个API URL
        base_url = self.wallet_api_url if use_wallet_url else self.api_url
        url = f"{base_url}{endpoint}"
        
        # 合并自定义headers到session headers
        request_headers = {**self.session.headers.copy(), **headers}
        
        try:
            timeout = (self.connect_timeout, self.read_timeout)
            
            if method.upper() == "GET":
                response = self.session.get(url, params=params, headers=request_headers, timeout=timeout)
            elif method.upper() == "POST":
                response = self.session.post(url, json=params, headers=request_headers, timeout=timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 验证HTTP状态码
            if response.status_code == 200:
                try:
                    return response.json()
                except Exception as e:
                    self.logger.error(f"❌ [NINE CEX] JSON解析失败: {e}")
                    self.logger.error(f"❌ [NINE CEX] 原始响应: {response.text[:500]}")
                    return None
            else:
                self.logger.error(f"❌ [NINE CEX] HTTP错误 {response.status_code}: {response.text[:500]}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ [NINE CEX] 请求异常: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 未知错误: {e}")
            return None
    
    def _request(self, endpoint: str, payload_data: dict | list | None, method: str = "POST") -> dict | None:
        """
        发送HTTP请求到Nine CEX API。

        Args:
            endpoint (str): API端点路径 (例如 "/batch/trade")。
            payload_data (dict | list | None): 请求的JSON数据。
            method (str): HTTP方法，默认为"POST"。

        Returns:
            dict | None: 响应的JSON数据，或者在出错时返回None。

        Raises:
            ConnectionError: 网络连接错误。
            ValueError: 请求参数错误或响应格式无效。
            RuntimeError: 其他未知错误。
        """
        url = f"{self.api_url}{endpoint}"
        
        
        try:
            # 使用配置的超时时间
            timeout = (self.connect_timeout, self.read_timeout)

            if method.upper() == "POST":
                response = self.session.post(url, json=payload_data, timeout=timeout)
            elif method.upper() == "GET":
                response = self.session.get(url, params=payload_data, timeout=timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            
            # 验证HTTP状态码
            if response.status_code == 200:
                try:
                    return response.json()
                except Exception as e:
                    self.logger.error(f"❌ [NINE CEX] JSON解析失败: {e}")
                    self.logger.error(f"❌ [NINE CEX] 原始响应: {response.text}")
                    return None
            elif response.status_code == 404:
                self.logger.error(f"❌ [NINE CEX] API端点未找到: {endpoint}")
                return None
            elif response.status_code >= 500:
                self.logger.error(f"❌ [NINE CEX] 服务器错误: {response.status_code}")
                return None
            else:
                self.logger.error(f"❌ [NINE CEX] HTTP错误: {response.status_code}")
                return None
                
        except requests.exceptions.ConnectTimeout:
            self.logger.error(f"❌ [NINE CEX] 连接超时: {url}")
            return None
        except requests.exceptions.ReadTimeout:
            self.logger.error(f"❌ [NINE CEX] 读取超时: {url}")
            return None
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"❌ [NINE CEX] 连接错误: {e}")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ [NINE CEX] 请求异常: {e}")
            return None
        except ValueError as e:
            self.logger.error(f"❌ [NINE CEX] JSON解析错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"请求异常: {e}")
            return None

    def _sanitize_payload_for_logging(self, payload):
        """
        清理payload中的敏感信息用于安全日志记录
        
        Args:
            payload: 原始payload数据
            
        Returns:
            清理后的payload（敏感字段被掩码）
        """
        import copy
        import json
        
        try:
            # 深拷贝避免修改原始数据
            safe_payload = copy.deepcopy(payload)
            
            def mask_sensitive_data(obj):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if key.lower() in ['secret', 'password', 'token', 'apikey']:
                            if isinstance(value, str) and len(value) > 8:
                                obj[key] = value[:4] + "****" + value[-4:]
                            else:
                                obj[key] = "****"
                        elif isinstance(value, (dict, list)):
                            mask_sensitive_data(value)
                elif isinstance(obj, list):
                    for item in obj:
                        if isinstance(item, (dict, list)):
                            mask_sensitive_data(item)
            
            mask_sensitive_data(safe_payload)
            return safe_payload
            
        except Exception:
            # 如果处理失败，返回通用占位符
            return "[payload数据无法安全显示]"

    def batch_trade(self, trade_operations: list[dict]):
        """
        执行批量交易。
        API 端点: /batch/trade
        payload 直接是操作列表，每个操作包含 userBean 和 tradeBean 列表。

        Args:
            trade_operations (list[dict]): 交易操作列表。
                根据 README.md，列表中的每个元素是一个字典，包含:
                - "userBean": {"apiKey": "...", "secret": "..."}
                - "tradeBean": [ 一个或多个交易详情 ]
                  每个交易详情字典包含: "accountType", "tradePairName", "tradePairId", 
                                       "orderDirection", "orderType", "orderQuantity", "orderPrice"。

        Returns:
            dict: API 的响应。
        """
        if not isinstance(trade_operations, list) or not all(isinstance(op, dict) for op in trade_operations):
            raise ValueError("trade_operations 必须是一个字典列表。")
        
        for operation in trade_operations:
            if not isinstance(operation.get("userBean"), dict) or \
               not all(k in operation["userBean"] for k in ["apiKey", "secret"]):
                raise ValueError("每个交易操作必须包含一个有效的 'userBean' (含 'apiKey' 和 'secret')。")
            if not isinstance(operation.get("tradeBean"), list) or \
               not all(isinstance(trade, dict) for trade in operation["tradeBean"]):
                raise ValueError("每个交易操作必须包含一个有效的 'tradeBean' (交易详情列表)。")
            for trade_detail in operation["tradeBean"]:
                required_keys = ["accountType", "tradePairName", "tradePairId", "orderDirection", "orderType", "orderQuantity", "orderPrice"]
                if not all(key in trade_detail for key in required_keys):
                    raise ValueError(f"每个 tradeBean 中的交易详情必须包含所有必需字段: {', '.join(required_keys)}。出错的详情: {trade_detail}")

        # 根据 README，payload 是一个列表
        return self._request("/batch/trade", trade_operations)

    def _parse_response(self, response: dict, operation_name: str = "API调用") -> Optional[Dict[str, Any]]:
        """
        解析Nine CEX API的响应结构（支持多种格式）。
        
        支持的响应格式:
        1. 简单两层结构 (现在的API格式):
        {
            "code": 200,
            "data": {...},          // 直接包含业务数据
            "message": "成功"
        }
        
        2. 三层嵌套结构 (某些API可能使用):
        {
            "code": 200,
            "data": {
                "apiKey": "...",
                "secret": "...",
                "data": {
                    "code": 200,
                    "data": {...}   // 实际业务数据
                }
            }
        }
        
        Args:
            response: API响应数据
            operation_name: 操作名称，用于错误日志
            
        Returns:
            实际的业务数据，如果解析失败返回None
        """
        
        if not response:
            self.logger.warning(f"⚠️ [NINE CEX] {operation_name} - 响应为空")
            return None
            
        # 检查是否为带认证信息的响应格式（包含apiKey, secret, data等字段）
        if isinstance(response, dict) and "data" in response and any(key in response.get("data", {}) for key in ['apiKey', 'secret']):
            data = response["data"]
            
            # 继续解析内层的data结构
            if isinstance(data, dict) and "data" in data:
                inner_data = data["data"]
                # 检查内层data是否为错误字符串
                if isinstance(inner_data, str) and ("Failed to login" in inner_data or "error" in inner_data.lower()):
                    self.logger.error(f"❌ [NINE CEX] {operation_name}API认证失败: {inner_data}")
                    return None
                elif isinstance(inner_data, dict):
                    # 检查是否还有更深层的嵌套
                    if inner_data.get("code") == 200 and "data" in inner_data:
                        data = inner_data["data"]
                    else:
                        data = inner_data
                else:
                    self.logger.warning(f"⚠️ [NINE CEX] {operation_name}内层data格式异常: {type(inner_data)}")
                    return None
        # 检查API级别状态码（标准格式）
        elif response.get("code") == 200:
            if "data" not in response:
                self.logger.error(f"❌ [NINE CEX] {operation_name}响应结构错误: 缺少data字段")
                self.logger.debug(f"🔍 [NINE CEX] 响应keys: {list(response.keys()) if isinstance(response, dict) else type(response)}")
                return None
            data = response["data"]
        else:
            self.logger.warning(f"⚠️ [NINE CEX] {operation_name}失败或格式异常: {response}")
            return None
        
        # 检查是否为简单两层结构 (包含orderDepth等订单簿字段)
        if isinstance(data, dict) and any(key in data for key in ['orderDepth', 'list', 'total', 'pageNum', 'pageSize', 'pages']):
            return data
            
        # 检查是否为三层嵌套结构
        if isinstance(data, dict) and "data" in data:
            inner_data = data["data"]
            
            if isinstance(inner_data, dict):
                # 检查内层是否有code和data字段（标准三层结构）
                if inner_data.get("code") == 200 and "data" in inner_data:
                    return inner_data["data"]
                # 检查内层是否直接包含业务字段
                elif any(key in inner_data for key in ['orderDepth', 'list', 'total', 'pageNum', 'pageSize', 'pages']):
                    return inner_data
                # 检查内层是否有message字段和data字段（另一种嵌套格式）
                elif "message" in inner_data and "data" in inner_data:
                    return inner_data["data"]
                else:
                    self.logger.warning(f"⚠️ [NINE CEX] {operation_name} - 内层data格式异常，直接返回")
                    # 直接返回内层数据，让调用方处理
                    return inner_data
        
        # 如果data是其他格式（可能直接就是业务数据）
        if isinstance(data, (dict, list)):
            return data
            
        self.logger.error(f"❌ [NINE CEX] {operation_name} - 无法解析的响应格式")
        return None

    def _parse_nested_response(self, response: dict, operation_name: str = "API调用") -> Optional[Dict[str, Any]]:
        """向后兼容的方法，内部调用新的_parse_response方法"""
        return self._parse_response(response, operation_name)

    def get_orders_trade_detail(self, trade_pair_id: str, user_api_key: str, user_api_secret: str, page_num: int = 1, page_size: int = 20) -> Optional[Dict[str, Any]]:
        """
        获取指定交易对的成交详情 (Get Order Trade Detail by Batch).
        API 端点: /batch/getOrdersTradeDetail

        Args:
            trade_pair_id (str): 交易对ID，例如 "20250617000029"。这个ID来自 getOuterPoolInfo 接口返回的 id 字段。
            user_api_key (str): 用户的 API Key。
            user_api_secret (str): 用户的 API Secret。
            page_num (int, optional): 页码。默认为 1。
            page_size (int, optional): 每页数量。默认为 20。

        Returns:
            dict: 解析后的成交数据，包含list和分页信息
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("获取成交详情需要 user_api_key 和 user_api_secret。")
        if not trade_pair_id:
            raise ValueError("trade_pair_id 不能为空")
        
        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "tradePair": trade_pair_id,  # 使用交易对ID
            "pageSize": page_size,
            "pageNum": page_num
        }
        
        self.logger.debug(f"🔍 [NINE CEX] 获取成交详情: 交易对ID={trade_pair_id}, 第{page_num}页，每页{page_size}条")
        
        response = self._request('/batch/getOrdersTradeDetail', payload_data=payload, method="POST")
        return self._parse_nested_response(response, "成交记录查询")

    def get_order_detail_by_id(self, order_id: str, user_api_key: str, user_api_secret: str) -> Optional[Dict[str, Any]]:
        """
        根据订单ID获取订单详情 (性能优化版本)。
        API 端点: /batch/getOrderDetailByOrderId

        Args:
            order_id (str): 要查询的订单ID。
            user_api_key (str): 用户的 API Key。
            user_api_secret (str): 用户的 API Secret。

        Returns:
            dict: 解析后的订单详情数据，包含订单状态、成交信息等
                  特殊字段：
                  - "exists": True/False 标识订单是否存在
                  - "query_success": True/False 标识查询是否成功
            None: 查询失败时返回None
        """
        if not order_id or not user_api_key or not user_api_secret:
            raise ValueError("获取订单详情需要 order_id、user_api_key 和 user_api_secret。")
        
        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "orderId": order_id
        }
        
        try:
            response = self._request('/batch/getOrderDetailByOrderId', payload_data=payload, method="POST")
            
            if not response:
                self.logger.warning(f"⚠️ [NINE CEX] 订单 {order_id} 查询响应为空")
                return {"exists": False, "query_success": False, "reason": "empty_response"}
            
            # 检查API级别的错误
            if response.get("code") != 200:
                error_msg = response.get("msg", "Unknown error")
                self.logger.warning(f"⚠️ [NINE CEX] 订单 {order_id} 查询API错误: {error_msg}")
                return {"exists": False, "query_success": False, "reason": f"api_error: {error_msg}"}
            
            # 手动解析三层嵌套结构
            data = response.get("data", {})
            if isinstance(data, dict) and "data" in data:
                inner_data = data["data"]
                if isinstance(inner_data, dict) and inner_data.get("code") == 200:
                    business_data = inner_data.get("data", {})
                    if isinstance(business_data, dict) and "orderId" in business_data:
                        # 订单存在，添加查询状态标识
                        business_data["exists"] = True
                        business_data["query_success"] = True
                        return business_data

            # 解析失败可能是订单不存在或格式错误
            self.logger.info(f"订单 {order_id} 不存在")
            return {"exists": False, "query_success": True, "reason": "order_not_found"}
            
        except ConnectionError as e:
            self.logger.warning(f"⚠️ [NINE CEX] 查询订单 {order_id} 网络连接失败: {e}")
            return {"exists": False, "query_success": False, "reason": f"connection_error: {str(e)}"}
        except ValueError as e:
            self.logger.warning(f"⚠️ [NINE CEX] 查询订单 {order_id} 数据格式错误: {e}")
            return {"exists": False, "query_success": False, "reason": f"data_format_error: {str(e)}"}
        except Exception as e:
            self.logger.warning(f"⚠️ [NINE CEX] 查询订单 {order_id} 详情失败: {e}")
            return {"exists": False, "query_success": False, "reason": f"unknown_error: {str(e)}"}

    def get_current_orders(self, trade_pair: str, user_api_key: str, user_api_secret: str, 
                          page_size: int = 100, page_num: int = 1, get_all_pages: bool = False) -> Optional[Dict[str, Any]]:
        """
        获取当前活跃订单列表
        API 端点: /trade/curorder/list (新版本)
        注意：API不支持symbol参数过滤，需要获取所有订单后本地过滤
        
        Args:
            trade_pair (str): 交易对名称，例如 "SOL/USDT" 或 "SOLUSDT"
            user_api_key (str): 用户的 API Key
            user_api_secret (str): 用户的 API Secret  
            page_size (int, optional): 每页数量，默认100
            page_num (int, optional): 页码，默认1
            get_all_pages (bool, optional): 是否获取所有页的订单，默认False
            
        Returns:
            dict: 当前订单列表数据，包含实际活跃的订单
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("获取当前订单需要 user_api_key 和 user_api_secret")
        if not trade_pair:
            raise ValueError("trade_pair 不能为空")
            
        # 规范化交易对格式: SOL/USDT -> SOLUSDT
        normalized_trade_pair = trade_pair.replace('/', '').replace(' ', '').upper()
        
        # 准备请求Headers
        headers = {
            'ApiKey': user_api_key,
            'Secret': user_api_secret,
            'Version': '1.0.1',
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        }
        
        all_filtered_orders = []  # 存储所有页过滤后的订单
        current_page = page_num
        total_pages = 1
        total_unfiltered_count = 0
        
        while True:
            # 准备查询参数（不包含symbol，因为API不支持）
            params = {
                'pageNum': current_page,
                'pageSize': page_size
            }
            
            self.logger.debug(f"📋 [获取当前订单] 查询第 {current_page} 页订单...")
            
            try:
                # 使用新的API端点，通过GET请求，使用wallet_api_url
                result = self._request_with_headers('/trade/curorder/list', params, headers, 'GET', use_wallet_url=True)
                
                if result and result.get("code") == 200:
                    data = result.get("data", {})
                    all_orders = data.get("list", [])
                    total_count = data.get("total", 0)
                    
                    # 第一页时记录总信息
                    if current_page == page_num:
                        total_pages = data.get("pages", 1)
                        total_unfiltered_count = total_count
                    
                    # 本地过滤指定交易对的订单
                    page_filtered_orders = []
                    for order in all_orders:
                        # 检查订单的交易对（可能的字段名：tradePair, symbol）
                        order_trade_pair = order.get('tradePair', '') or order.get('symbol', '')
                        if order_trade_pair.upper() == normalized_trade_pair:
                            page_filtered_orders.append(order)
                    
                    all_filtered_orders.extend(page_filtered_orders)
                    self.logger.debug(f"📊 第 {current_page} 页: 从 {len(all_orders)} 个订单中过滤出 {len(page_filtered_orders)} 个 {trade_pair} 订单")
                    
                    # 判断是否继续获取下一页
                    if not get_all_pages or current_page >= total_pages or len(all_orders) == 0:
                        break
                        
                    current_page += 1
                else:
                    self.logger.error(f"❌ [获取当前订单] 第 {current_page} 页API返回错误: {result}")
                    break
                    
            except Exception as e:
                self.logger.error(f"❌ [获取当前订单] 第 {current_page} 页请求异常: {e}")
                break
        
        # 记录最终结果
        self.logger.info(f"✅ [获取当前订单] 从总计 {total_unfiltered_count} 个订单中，过滤出 {trade_pair} 的 {len(all_filtered_orders)} 个活跃订单")
        
        return {
            "code": 200,
            "data": {
                "list": all_filtered_orders,
                "total": len(all_filtered_orders),
                "pageNum": page_num,
                "pageSize": page_size,
                "totalUnfiltered": total_unfiltered_count
            }
        }
            

    def cancel_all_orders(self, user_api_key: str, user_api_secret: str) -> dict:
        """
        通过 /batch/cancelAllOrder 端点批量撤销指定API Key下的所有订单。

        Args:
            user_api_key (str): 用户的 API Key。
            user_api_secret (str): 用户的 API Secret。

        Returns:
            dict: API 的响应。
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("全量撤单需要 user_api_key 和 user_api_secret。")

        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "orderId": [] # 空列表表示撤销所有订单
        }
        
        return self._request('/batch/cancelAllOrder', payload_data=payload, method="POST")

    def batch_cancel_orders(self, order_ids: List[str], user_api_key: str, user_api_secret: str) -> Optional[Dict[str, Any]]:
        """
        通过 /batch/cancelOrder 端点批量撤销指定的订单ID列表。

        Args:
            order_ids (list[str]): 要取消的订单ID字符串列表。
            user_api_key (str): 用户的 API Key。
            user_api_secret (str): 用户的 API Secret。

        Returns:
            dict: API 的响应。
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("指定订单撤单需要 user_api_key 和 user_api_secret。")
        if not isinstance(order_ids, list) or not all(isinstance(oid, str) for oid in order_ids):
            raise ValueError("order_ids 必须是一个字符串列表。")
        if not order_ids:
            self.logger.warning("⚠️ [NINE CEX] 撤单列表为空")
            return {"code": 200, "msg": "No orders specified to cancel.", "data": []}

        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "orderId": order_ids
        }
        
        return self._request('/batch/cancelOrder', payload_data=payload, method="POST")

    def get_orders_status(self, order_ids: list) -> dict:
        """
        批量获取订单状态信息。
        使用 get_order_detail_by_id 方法批量查询订单状态。

        Args:
            order_ids (list): 订单ID列表。

        Returns:
            dict: 包含所有订单状态的响应。
        """
        if not order_ids:
            return {"code": 200, "data": [], "message": "无订单ID"}

        # 批量查询订单状态
        order_statuses = []
        for order_id in order_ids:
            try:
                # 使用现有的 get_order_detail_by_id 方法
                # 注意：这里需要API凭证，从环境变量获取
                import os
                api_key = os.getenv('MM_NINE_API_KEY')
                api_secret = os.getenv('MM_NINE_API_SECRET')

                if not api_key or not api_secret:
                    self.logger.error("❌ [NINE CEX] 缺少API凭证")
                    continue

                order_detail = self.get_order_detail_by_id(order_id, api_key, api_secret)
                if order_detail:
                    order_statuses.append(order_detail)
            except Exception as e:
                self.logger.warning(f"⚠️ [NINE CEX] 获取订单 {order_id} 状态失败: {e}")
                continue

        return {"code": 200, "data": order_statuses, "message": f"查询到 {len(order_statuses)} 个订单状态"}

    def get_order_info(self, order_id: str) -> dict:
        """
        获取单个订单的详细信息。
        【注意】此方法的端点和payload需要确认Nine CEX是否提供相应API。
        当前实现是错误的，指向了批量撤单API。

        Args:
            order_id (str): 要查询的订单ID。

        Returns:
            dict: API的响应，包含订单详情。
        """
        self.logger.warning("⚠️ [NINE CEX] get_order_info 方法未实现")
        raise NotImplementedError("get_order_info 的 Nine CEX API 端点未知或未实现。")

    def get_latest_trade_price(self, trade_pair_id: str, user_api_key: str, user_api_secret: str) -> Optional[Decimal]:
        """
        获取指定交易对的最新成交价
        复用 get_outer_pool_latest_order 方法并从 quote.n 字段提取最新成交价
        
        Args:
            trade_pair_id: 交易对ID
            user_api_key: API密钥
            user_api_secret: API密钥
            
        Returns:
            Optional[Decimal]: 最新成交价，失败时返回None
        """
        try:
            # 复用 get_outer_pool_latest_order 方法获取完整数据
            business_data = self.get_outer_pool_latest_order(
                trade_pair_name="",  # 使用空字符串，优先使用trade_pair_id
                api_key=user_api_key,
                secret=user_api_secret,
                trade_pair_id=trade_pair_id,
                num=1,  # 只需要1层深度
                precision="0.00000001"  # 最高精度
            )
            
            if not business_data:
                return None
            
            # 提取quote中的最新成交价
            quote_info = business_data.get("quote", {})
            if quote_info and isinstance(quote_info, dict):
                latest_price_str = quote_info.get("n")  # n字段是最新成交价
                if latest_price_str and latest_price_str != "0" and latest_price_str != "0.00000000":
                    from decimal import Decimal
                    return Decimal(str(latest_price_str))
            
            return None
            
        except Exception as e:
            self.logger.warning(f"⚠️ [NINE CEX] 获取最新成交价失败: {e}")
            return None

    def get_outer_pool_latest_order(self, trade_pair_name: str, api_key: str, secret: str, trade_pair_id: Optional[str] = None, num: Optional[int] = None, precision: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取指定交易对的外部池子最新订单信息 (asks/bids)。
        API 端点: /batch/getOuterPoolLatestOrder

        Args:
            trade_pair_name: 交易对名称，如 "RIO/USDT"
            api_key: API密钥
            secret: API密钥
            trade_pair_id: 交易对ID，如 "20250701000002"。如果不提供，会尝试自动获取
            num: 订单簿深度
            precision: 价格精度字符串，如 "0.00000001"（最高精度）或 "0.01"（较低精度）

        Returns:
            订单簿数据 {"asks": [], "bids": []}
        """
        # 使用公共方法处理交易对数据
        pair_data = self._prepare_trading_pair_request(trade_pair_name, api_key, secret, trade_pair_id)
        if not pair_data:
            return None
            
        endpoint = "/batch/getOuterPoolLatestOrder"
        
        payload = {
            "apiKey": api_key,
            "secret": secret,
            "tradePairName": pair_data["base_name"],
            "tradePairId": pair_data["resolved_pair_id"],
            "num": num if num is not None else 20,
            "precision": self._get_safe_precision(precision, trade_pair_name)
        }

        self.logger.info(f"获取订单簿: {trade_pair_name}")

        try:
            response = self._request(endpoint, payload_data=payload)
            
            business_data = self._parse_nested_response(response, "订单簿查询")
            
            if not business_data:
                self.logger.warning(f"⚠️ [NINE CEX] 未获取到业务数据: {trade_pair_name}")
                return None
            
            if business_data and "orderDepth" in business_data and isinstance(business_data["orderDepth"], dict):
                order_depth = business_data["orderDepth"]
                
                # 验证 asks 和 bids 数据
                if ("asks" in order_depth and isinstance(order_depth["asks"], list) and
                    "bids" in order_depth and isinstance(order_depth["bids"], list)):
                    
                    self.logger.info(f"订单簿成功 - bids:{len(order_depth['bids'])}, asks:{len(order_depth['asks'])}")
                    # 缓存成功使用的precision
                    self._cache_valid_precision(payload["precision"], trade_pair_name)
                    # 返回完整的business_data，包含orderDepth和quote信息
                    return business_data
                else:
                    self.logger.debug(f"🔍 [NINE CEX] orderDepth 内数据格式错误 - asks存在:{('asks' in order_depth)}, bids存在:{('bids' in order_depth)}")
                    self.logger.debug(f"🔍 [NINE CEX] asks类型检查: {isinstance(order_depth.get('asks'), list)}, bids类型检查: {isinstance(order_depth.get('bids'), list)}")
                    return None
            else:
                self.logger.debug(f"🔍 [NINE CEX] 缺少 orderDepth 字段或数据格式错误")
                if business_data:
                    self.logger.debug(f"🔍 [NINE CEX] business_data keys: {list(business_data.keys())}")
                return None
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 获取订单簿异常: {e}")
            return None

    def _prepare_trading_pair_request(self, trade_pair_name: str, api_key: str, secret: str, trade_pair_id: Optional[str] = None) -> Dict[str, str]:
        """
        准备交易对请求的通用数据处理
        
        Args:
            trade_pair_name: 交易对名称
            api_key: API密钥
            secret: API密钥  
            trade_pair_id: 交易对ID（可选）
            
        Returns:
            包含resolved_pair_id和base_name的字典
        """
        # 解析交易对ID
        resolved_pair_id = trade_pair_id
        if not resolved_pair_id:
            try:
                from app.services.trading_pair_manager import TradingPairManager
                temp_manager = TradingPairManager(self, self.logger)
                temp_manager.set_default_credentials(api_key, secret)
                resolved_pair_id = temp_manager.get_pair_id(trade_pair_name, api_key, secret)
                
                if resolved_pair_id and resolved_pair_id != trade_pair_name:
                    self.logger.debug(f"🔄 [NINE CEX] 自动解析交易对ID: {trade_pair_name} -> {resolved_pair_id}")
                else:
                    resolved_pair_id = trade_pair_name
                    self.logger.warning(f"⚠️ [NINE CEX] 无法获取 {trade_pair_name} 的交易对ID，使用原始名称")
            except Exception as e:
                self.logger.warning(f"⚠️ [NINE CEX] 解析交易对ID失败: {e}，使用原始名称")
                resolved_pair_id = trade_pair_name
        
        # 统一格式处理
        from .trading_pair_normalizer import TradingPairNormalizer
        normalizer = TradingPairNormalizer(self.logger)
        normalized = normalizer.normalize(trade_pair_name) or normalizer.normalize(f"{trade_pair_name}/USDT")
        if not normalized:
            self.logger.error(f"无法标准化交易对: {trade_pair_name}")
            # 使用原始交易对名称作为tradePairName
            return {"resolved_pair_id": resolved_pair_id, "base_name": trade_pair_name}
        
        # 根据API文档，tradePairName需要完整的交易对名称（如"二哈/USDT"）
        # 不是base_name（如"二哈"）
        return {"resolved_pair_id": resolved_pair_id, "base_name": trade_pair_name}

    def _get_safe_precision(self, precision: Optional[str], trade_pair_name: str) -> float:
        """
        获取安全的precision值，使用简单的策略
        
        Args:
            precision: 用户提供的precision（可选）
            trade_pair_name: 交易对名称（用于缓存）
            
        Returns:
            安全的precision数字
        """
        # 常用精度列表，按精度从高到低排序（数字格式）
        common_precisions = [0.000001, 0.00001, 0.0001, 0.001, 0.01]
        
        # 如果用户提供了precision，转换为数字并验证
        if precision:
            try:
                precision_float = float(precision)
                if precision_float in common_precisions:
                    return precision_float
            except (ValueError, TypeError):
                pass
        
        # 检查是否有缓存的有效precision
        cache_key = f"valid_precision_{trade_pair_name}"
        if hasattr(self, '_precision_cache') and cache_key in self._precision_cache:
            cached_precision = self._precision_cache[cache_key]
            self.logger.debug(f"🔄 [NINE CEX] 使用缓存精度: {cached_precision}")
            return float(cached_precision)
        
        # 返回最常用的高精度值
        return 0.000001
    
    def _cache_valid_precision(self, precision: str, trade_pair_name: str):
        """缓存验证成功的precision"""
        if not hasattr(self, '_precision_cache'):
            self._precision_cache = {}
        
        cache_key = f"valid_precision_{trade_pair_name}"
        self._precision_cache[cache_key] = precision

    def close(self):
        """关闭requests会话，释放连接池资源"""
        if hasattr(self, 'session') and self.session:
            self.session.close()

    def __enter__(self):
        """上下文管理器支持"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出时自动关闭会话"""
        self.close()

    def get_outer_pool_info_all(self, user_api_key: str, user_api_secret: str, trade_pair_type: int = 1, page_num: int = 1, page_size: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取所有外部交易对池信息 (监控新交易对)。
        API 端点: /batch/getOuterPoolInfo

        Args:
            user_api_key (str): 用户API密钥。
            user_api_secret (str): 用户API密钥。
            trade_pair_type (int, optional): 交易对类型。默认为 1。
                - 0: 内盘交易对（平台内部特殊交易对）
                - 1: 外盘交易对（普通币币交易对）
                - 2: 其他类型交易对
            page_num (int, optional): 页码。默认为 1。
            page_size (int, optional): 每页数量。默认为 100。

        Returns:
            dict: 解析后的API响应数据，包含交易对列表，每个交易对包含 id 字段。
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("用户API密钥和密钥不能为空")
        if page_num < 1:
            raise ValueError("页码必须大于等于1")
        if page_size < 1 or page_size > 1000:
            raise ValueError("每页数量必须在1-1000之间")
        if trade_pair_type not in [0, 1, 2]:
            raise ValueError("交易对类型必须是 0、1 或 2")

        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "tradePairType": trade_pair_type,
            "pageSize": page_size,
            "pageNum": page_num
        }

        self.logger.debug(f"🔍 [NINE CEX] 获取所有外盘交易对信息: 类型{trade_pair_type}, 第{page_num}页，每页{page_size}条")
        
        try:
            response = self._request("/batch/getOuterPoolInfo", payload)
            if response:
                # 解析嵌套响应结构
                return self._parse_nested_response(response, "获取所有外盘交易对信息")
            return None
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 获取外盘交易对信息失败: {e}")
            raise

    def get_outer_pool_info_by_group(self, user_api_key: str, user_api_secret: str, group_name: str, trade_pair_type: int = 1, page_num: int = 1, page_size: int = 100) -> Optional[Dict[str, Any]]:
        """
        按组名获取外部交易对池信息。
        API 端点: /batch/getOuterPoolInfo

        Args:
            user_api_key (str): 用户API密钥。
            user_api_secret (str): 用户API密钥。
            group_name (str): 交易对组名，例如 "SEPUSDT"、"USDT"。
            trade_pair_type (int, optional): 交易对类型。默认为 1。
                - 0: 内盘交易对（平台内部特殊交易对）
                - 1: 外盘交易对（普通币币交易对）
                - 2: 其他类型交易对
            page_num (int, optional): 页码。默认为 1。
            page_size (int, optional): 每页数量。默认为 100。

        Returns:
            dict: 解析后的API响应数据，包含指定组的交易对列表，每个交易对包含 id 字段。
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("用户API密钥和密钥不能为空")
        if not group_name:
            raise ValueError("group_name 不能为空")
        if page_num < 1:
            raise ValueError("页码必须大于等于1")
        if page_size < 1 or page_size > 1000:
            raise ValueError("每页数量必须在1-1000之间")
        if trade_pair_type not in [0, 1, 2]:
            raise ValueError("交易对类型必须是 0、1 或 2")

        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "groupName": group_name,
            "tradePairType": trade_pair_type,
            "pageSize": page_size,
            "pageNum": page_num
        }

        self.logger.debug(f"🔍 [NINE CEX] 获取外盘交易对信息 (组: {group_name}, 类型: {trade_pair_type}): 第{page_num}页，每页{page_size}条")
        
        try:
            response = self._request("/batch/getOuterPoolInfo", payload)
            if response:
                # 解析嵌套响应结构
                return self._parse_nested_response(response, f"获取外盘交易对信息({group_name})")
            return None
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 获取外盘交易对信息失败 (组: {group_name}): {e}")
            raise

    def get_inner_pool_info(self, user_api_key: str, user_api_secret: str, current_page: int = 1, page_size: int = 20, chain: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        批量获取内盘信息 (Get Inner Pool Info).
        API 端点: /batch/getInnerPoolInfo

        Args:
            user_api_key (str): 用户的 API Key。
            user_api_secret (str): 用户的 API Secret。
            current_page (int, optional): 当前页码。默认为 1。
            page_size (int, optional): 每页数量。默认为 20。
            chain (str, optional): 链名称，例如 "Solana"。

        Returns:
            dict: 解析后的内盘数据，包含内盘池子信息和分页信息
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("用户API密钥和密钥不能为空")
        if current_page < 1:
            raise ValueError("页码必须大于等于1")
        if page_size < 1 or page_size > 1000:
            raise ValueError("每页数量必须在1-1000之间")

        payload = {
            "apiKey": user_api_key,
            "secret": user_api_secret,
            "currentPage": current_page,
            "pageSize": page_size
        }
        
        if chain:
            payload["chain"] = chain

        self.logger.debug(f"🔍 [NINE CEX] 获取内盘信息: 第{current_page}页，每页{page_size}条" + (f"，链: {chain}" if chain else ""))

        try:
            response = self._request("/batch/getInnerPoolInfo", payload)
            if response:
                # 解析标准嵌套响应结构
                return self._parse_nested_response(response, "获取内盘信息")
            return None
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 获取内盘信息失败: {e}")
            raise

    def batch_inner_disk_trade(self, trade_operations: List[Dict]) -> dict:
        """
        执行批量内盘交易 (Batch Inner Disk Trade).
        API 端点: /batch/trade/innerDisk

        Args:
            trade_operations (List[Dict]): 内盘交易操作列表。
                列表中的每个元素是一个字典，包含:
                - "userBean": {"apiKey": "...", "secret": "..."}
                - "innerTradeBean": [
                    {"poolId": int, "amount": str, "tradeType": "BUY"/"SELL"}
                  ]

        Returns:
            dict: API 的响应。
        """
        if not isinstance(trade_operations, list) or not all(isinstance(op, dict) for op in trade_operations):
            raise ValueError("trade_operations 必须是一个字典列表。")
        
        for operation in trade_operations:
            if not isinstance(operation.get("userBean"), dict) or \
               not all(k in operation["userBean"] for k in ["apiKey", "secret"]):
                raise ValueError("每个交易操作必须包含一个有效的 'userBean' (含 'apiKey' 和 'secret')。")
            if not isinstance(operation.get("innerTradeBean"), list) or \
               not all(isinstance(trade, dict) for trade in operation["innerTradeBean"]):
                raise ValueError("每个交易操作必须包含一个有效的 'innerTradeBean' (内盘交易详情列表)。")
            for trade_detail in operation["innerTradeBean"]:
                required_keys = ["poolId", "amount", "tradeType"]
                if not all(key in trade_detail for key in required_keys):
                    raise ValueError(f"每个 innerTradeBean 中的交易详情必须包含所有必需字段: {', '.join(required_keys)}。出错的详情: {trade_detail}")
                if trade_detail["tradeType"] not in ["BUY", "SELL"]:
                    raise ValueError(f"tradeType 必须是 'BUY' 或 'SELL'，当前值: {trade_detail['tradeType']}")

        self.logger.debug(f"📤 [NINE CEX] 发送 {len(trade_operations)} 个内盘交易批量请求...")
        return self._request("/batch/trade/innerDisk", trade_operations)
    
    def get_orders(self, trade_pair_id: str = None, trade_pair_name: str = None, 
                   user_api_key: str = None, user_api_secret: str = None,
                   num: Optional[int] = None, precision: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取指定交易对的订单簿数据（get_outer_pool_latest_order的兼容性包装器）
        
        Args:
            trade_pair_id: 交易对ID（优先级高于trade_pair_name）
            trade_pair_name: 交易对名称
            user_api_key: API密钥
            user_api_secret: API密钥
            num: 订单簿深度
            precision: 价格精度
            
        Returns:
            订单簿数据格式：
            {
                "code": 200,
                "data": {
                    "asks": [[price, quantity], ...],
                    "bids": [[price, quantity], ...]
                }
            }
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("获取订单簿需要 user_api_key 和 user_api_secret")
        
        # 确定使用的交易对标识
        if trade_pair_id:
            pair_name_for_api = trade_pair_id  # API可能需要名称
            pair_id_for_api = trade_pair_id
        elif trade_pair_name:
            pair_name_for_api = trade_pair_name
            pair_id_for_api = trade_pair_name  # 如果没有ID，使用名称作为后备
        else:
            raise ValueError("必须提供 trade_pair_id 或 trade_pair_name")
        
        try:
            # 调用现有的订单簿获取方法
            business_data = self.get_outer_pool_latest_order(
                trade_pair_name=pair_name_for_api,
                api_key=user_api_key,
                secret=user_api_secret,
                trade_pair_id=pair_id_for_api,
                num=num,
                precision=precision
            )
            
            if business_data and isinstance(business_data, dict):
                # 包装为标准API响应格式，传递完整的business_data
                return {
                    "code": 200,
                    "data": business_data,
                    "msg": "success"
                }
            else:
                # 返回空订单簿响应
                return {
                    "code": 200,
                    "data": {"asks": [], "bids": []},
                    "msg": "empty_order_book"
                }
                
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 获取订单簿失败: {e}")
            # 返回错误响应
            return {
                "code": 400,
                "data": {"asks": [], "bids": []},
                "msg": f"error: {str(e)}"
            }
    
    def get_wallet_account(self, user_api_key: str, user_api_secret: str) -> Optional[Dict[str, Any]]:
        """
        查询用户钱包余额
        
        Args:
            user_api_key (str): 用户的 API Key。
            user_api_secret (str): 用户的 API Secret。
            
        Returns:
            Dict: 余额信息响应
            {
                "code": 200,
                "data": {
                    "fundingBalanceInfo": {
                        "tokenList": [
                            {
                                "asset": "USDT",
                                "amount": "1000.00",
                                "availableAmount": "1000.00",
                                "usdValue": 1000.00
                            },
                            ...
                        ],
                        "accountBalance": 1000.00
                    },
                    "tradingBalanceInfo": {
                        "tokenList": [],
                        "accountBalance": 0
                    },
                    "totalBalance": 1000.00
                },
                "msg": "success"
            }
        """
        if not user_api_key or not user_api_secret:
            raise ValueError("查询余额需要 user_api_key 和 user_api_secret")
        
        # 构建请求头，余额查询API使用headers认证
        headers = {
            'ApiKey': user_api_key,
            'Secret': user_api_secret,
            'Version': '1.0.1',
            'site': 'MAIN',
            'Accept': '*/*'
        }
        
        try:
            self.logger.info(f"💰 [NINE CEX] 查询用户余额...")
            
            # 使用钱包API URL（与交易API分离）
            timeout = (self.connect_timeout, self.read_timeout)
            response = self.session.get(
                f"{self.wallet_api_url}/wallet/account",
                headers=headers,
                timeout=timeout
            )
            
            # 验证HTTP状态码
            if response.status_code == 200:
                try:
                    result = response.json()
                    if isinstance(result, dict):
                        self.logger.info(f"✅ [NINE CEX] 余额查询成功")
                        return result
                    else:
                        self.logger.error(f"❌ [NINE CEX] 余额查询响应格式错误")
                        return None
                except Exception as json_error:
                    self.logger.error(f"❌ [NINE CEX] 余额查询JSON解析失败: {json_error}")
                    return None
            else:
                self.logger.error(f"❌ [NINE CEX] 余额查询HTTP错误: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ [NINE CEX] 余额查询异常: {e}")
            return None 