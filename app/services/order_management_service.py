"""
订单管理服务 (Order Management Service)

功能：
1. 本地订单状态管理和持久化存储
2. 策略间订单追踪和冲突检测
3. API调用优化和缓存管理
4. 订单生命周期管理
"""

import sqlite3
import time
import json
import threading
import hashlib
import queue
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal
from pathlib import Path
from contextlib import contextmanager
import logging


@dataclass
class OrderRecord:
    """订单记录数据结构"""
    order_id: str
    strategy_id: str
    strategy_type: str  # 'GL', 'AK', etc
    trading_pair: str
    direction: int      # 1=买, 2=卖
    price: str         # 使用字符串避免精度问题
    quantity: str      # 使用字符串避免精度问题
    status: str        # 'pending', 'active', 'filled', 'cancelled'
    created_at: float
    updated_at: float
    filled_quantity: str = "0"
    avg_fill_price: str = "0"
    metadata: str = "{}"  # JSON格式的额外数据
    version: int = 0   # 版本控制，支持乐观锁


@dataclass
class StrategyMetadata:
    """策略元数据"""
    strategy_id: str
    strategy_type: str
    config: str         # JSON格式的配置
    active: bool
    created_at: float
    updated_at: float


class VersionConflictError(Exception):
    """版本冲突异常"""
    pass


class ConnectionPool:
    """数据库连接池，提高并发性能"""
    
    def __init__(self, db_path: str, pool_size: int = 5):
        self.db_path = db_path
        self.pool = queue.Queue(maxsize=pool_size)
        self.lock = threading.RLock()
        
        # 预创建连接
        for _ in range(pool_size):
            conn = sqlite3.connect(db_path, check_same_thread=False)
            # 启用WAL模式提高并发读写性能
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            self.pool.put(conn)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = self.pool.get()
        try:
            yield conn
        finally:
            self.pool.put(conn)
    
    def close_all(self):
        """关闭所有连接"""
        while not self.pool.empty():
            conn = self.pool.get()
            conn.close()


class AtomicTransaction:
    """原子事务管理器，确保缓存和数据库操作的一致性"""
    
    def __init__(self, oms):
        self.oms = oms
        self.conn = None
        self._cache_backup = None
        
    def __enter__(self):
        # 按固定顺序获取锁，避免死锁
        self.oms._db_lock.acquire()
        self.oms._cache_lock.acquire()
        
        # 备份当前缓存状态
        self._cache_backup = {
            'order_cache': self.oms._order_cache.copy(),
            'strategy_cache': self.oms._strategy_cache.copy(),
            'active_orders_by_strategy': {k: v.copy() for k, v in self.oms._active_orders_by_strategy.items()}
        }
        
        # 获取数据库连接并开始事务
        self.conn = self.oms.connection_pool.pool.get()
        self.conn.execute("BEGIN IMMEDIATE")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            if exc_type is None:
                # 提交事务
                self.conn.execute("COMMIT")
            else:
                # 回滚数据库事务
                self.conn.execute("ROLLBACK")
                # 恢复缓存状态
                self._rollback_cache()
        except Exception as e:
            self.oms.logger.error(f"❌ 事务处理异常: {e}")
            # 确保回滚缓存
            self._rollback_cache()
        finally:
            # 归还数据库连接
            self.oms.connection_pool.pool.put(self.conn)
            # 释放锁（与获取顺序相反）
            self.oms._cache_lock.release()
            self.oms._db_lock.release()
    
    def _rollback_cache(self):
        """回滚缓存到事务开始前的状态"""
        if self._cache_backup:
            self.oms._order_cache = self._cache_backup['order_cache']
            self.oms._strategy_cache = self._cache_backup['strategy_cache']
            self.oms._active_orders_by_strategy = self._cache_backup['active_orders_by_strategy']


def generate_stable_strategy_id(strategy) -> str:
    """生成稳定的策略ID，支持重启后恢复"""
    # 使用策略类型 + 交易对 + API Key前缀作为唯一标识
    api_key_prefix = getattr(strategy, 'api_key', 'default')[:8] if hasattr(strategy, 'api_key') else 'default'
    trading_pair = getattr(strategy, 'trading_pair', 'unknown') if hasattr(strategy, 'trading_pair') else 'unknown'
    
    identifier = f"{strategy.__class__.__name__}_{trading_pair}_{api_key_prefix}"
    hash_suffix = hashlib.md5(identifier.encode()).hexdigest()[:8]
    return f"{strategy.__class__.__name__}_{hash_suffix}"


class OrderManagementService:
    """订单管理服务"""
    
    def __init__(self, db_path: str = "data/orders.db"):
        """
        初始化订单管理服务
        
        Args:
            db_path: SQLite数据库文件路径
        """
        # 首先初始化logger
        self.logger = logging.getLogger(__name__)
        
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 内存缓存
        self._order_cache: Dict[str, OrderRecord] = {}
        self._strategy_cache: Dict[str, StrategyMetadata] = {}
        self._active_orders_by_strategy: Dict[str, List[str]] = {}
        self._cache_lock = threading.RLock()
        
        # 数据库连接池和锁
        self._db_lock = threading.RLock()
        self.connection_pool = ConnectionPool(str(self.db_path), pool_size=5)
        
        # 初始化数据库
        self._init_database()
        
        # 加载现有数据到缓存
        self._load_cache()
        
        self.logger.info(f"✅ OrderManagementService初始化完成，数据库: {self.db_path}")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self.connection_pool.get_connection() as conn:
            
            # 订单表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    order_id TEXT PRIMARY KEY,
                    strategy_id TEXT NOT NULL,
                    strategy_type TEXT NOT NULL,
                    trading_pair TEXT NOT NULL,
                    direction INTEGER NOT NULL,
                    price TEXT NOT NULL,
                    quantity TEXT NOT NULL,
                    status TEXT NOT NULL,
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL,
                    filled_quantity TEXT DEFAULT '0',
                    avg_fill_price TEXT DEFAULT '0',
                    metadata TEXT DEFAULT '{}',
                    version INTEGER DEFAULT 0
                )
            """)
            
            # 策略元数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_metadata (
                    strategy_id TEXT PRIMARY KEY,
                    strategy_type TEXT NOT NULL,
                    config TEXT NOT NULL,
                    active BOOLEAN NOT NULL,
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL
                )
            """)
            
            # 策略会话表（跟踪策略运行状态）
            conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_sessions (
                    session_id TEXT PRIMARY KEY,
                    strategy_id TEXT NOT NULL,
                    process_id INTEGER NOT NULL,
                    started_at REAL NOT NULL,
                    heartbeat_at REAL NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active'
                )
            """)
            
            # 创建优化索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_strategy ON orders(strategy_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_pair ON orders(trading_pair)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_created ON orders(created_at)")
            # 复合索引，提高多条件查询性能
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_strategy_time ON orders(strategy_id, created_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_pair_status ON orders(trading_pair, status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_version ON orders(version)")
            
            # 表创建完成后执行数据库迁移
            self._migrate_database(conn)
    
    def _migrate_database(self, conn):
        """执行数据库迁移"""
        # 检查orders表是否存在version列
        cursor = conn.execute("PRAGMA table_info(orders)")
        columns = {row[1]: row[2] for row in cursor.fetchall()}
        
        if 'version' not in columns:
            self.logger.info("🔄 执行数据库迁移: 添加version列到orders表")
            try:
                # 添加version列
                conn.execute("ALTER TABLE orders ADD COLUMN version INTEGER DEFAULT 0")
                
                # 更新现有记录的version为0
                conn.execute("UPDATE orders SET version = 0 WHERE version IS NULL")
                
                # 添加version索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_orders_version ON orders(version)")
                
                conn.commit()
                self.logger.info("✅ 数据库迁移完成: version列已添加")
                
            except Exception as e:
                self.logger.error(f"❌ 数据库迁移失败: {e}")
                conn.rollback()
                raise
        else:
            self.logger.debug("📋 数据库架构已是最新版本")
            
            # 策略会话表索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_strategy ON strategy_sessions(strategy_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_sessions_status ON strategy_sessions(status)")
            
            # 检查并添加version列（向后兼容）
            try:
                conn.execute("ALTER TABLE orders ADD COLUMN version INTEGER DEFAULT 0")
            except sqlite3.OperationalError:
                # 列已存在，忽略错误
                pass
            
            conn.commit()
    
    def _load_cache(self):
        """加载数据库数据到内存缓存"""
        with self._cache_lock:
            # 加载活跃订单
            with self.connection_pool.get_connection() as conn:
                conn.row_factory = sqlite3.Row
                
                # 加载订单（处理version字段可能不存在的情况）
                try:
                    cursor = conn.execute("""
                        SELECT * FROM orders 
                        WHERE status IN ('pending', 'active')
                        ORDER BY created_at DESC
                    """)
                except sqlite3.OperationalError:
                    # 如果version字段不存在，使用旧查询
                    cursor = conn.execute("""
                        SELECT order_id, strategy_id, strategy_type, trading_pair, direction,
                               price, quantity, status, created_at, updated_at, 
                               filled_quantity, avg_fill_price, metadata, 0 as version
                        FROM orders 
                        WHERE status IN ('pending', 'active')
                        ORDER BY created_at DESC
                    """)
                
                for row in cursor:
                    row_dict = dict(row)
                    # 确保version字段存在
                    if 'version' not in row_dict:
                        row_dict['version'] = 0
                    order = OrderRecord(**row_dict)
                    self._order_cache[order.order_id] = order
                    
                    # 按策略分组
                    if order.strategy_id not in self._active_orders_by_strategy:
                        self._active_orders_by_strategy[order.strategy_id] = []
                    self._active_orders_by_strategy[order.strategy_id].append(order.order_id)
                
                # 加载策略元数据
                cursor = conn.execute("SELECT * FROM strategy_metadata WHERE active = 1")
                for row in cursor:
                    metadata = StrategyMetadata(**dict(row))
                    self._strategy_cache[metadata.strategy_id] = metadata
        
        self.logger.info(f"📚 缓存加载完成: {len(self._order_cache)}个订单, {len(self._strategy_cache)}个策略")
    
    def register_strategy(self, strategy_id: str, strategy_type: str, config: Dict[str, Any]) -> bool:
        """
        注册策略
        
        Args:
            strategy_id: 策略唯一标识
            strategy_type: 策略类型 ('GL', 'AK', 'LP', etc)
            config: 策略配置字典
        
        Returns:
            bool: 注册是否成功
        """
        try:
            current_time = time.time()
            metadata = StrategyMetadata(
                strategy_id=strategy_id,
                strategy_type=strategy_type,
                config=json.dumps(config),
                active=True,
                created_at=current_time,
                updated_at=current_time
            )
            
            with self._cache_lock:
                self._strategy_cache[strategy_id] = metadata
                if strategy_id not in self._active_orders_by_strategy:
                    self._active_orders_by_strategy[strategy_id] = []
            
            # 持久化到数据库
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO strategy_metadata 
                    (strategy_id, strategy_type, config, active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    metadata.strategy_id, metadata.strategy_type, metadata.config,
                    metadata.active, metadata.created_at, metadata.updated_at
                ))
                conn.commit()
            
            self.logger.info(f"📝 策略注册成功: {strategy_id} ({strategy_type})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 策略注册失败: {e}")
            return False
    
    def record_order_creation(self, order_id: str, strategy_id: str, trade_bean: Dict[str, Any]) -> bool:
        """
        记录订单创建（原子事务）
        
        Args:
            order_id: 订单ID
            strategy_id: 创建该订单的策略ID
            trade_bean: 订单数据
        
        Returns:
            bool: 记录是否成功
        """
        try:
            current_time = time.time()
            
            # 获取策略类型
            strategy_type = "UNKNOWN"
            if strategy_id in self._strategy_cache:
                strategy_type = self._strategy_cache[strategy_id].strategy_type
            
            order = OrderRecord(
                order_id=order_id,
                strategy_id=strategy_id,
                strategy_type=strategy_type,
                trading_pair=trade_bean.get('tradePairName', ''),
                direction=trade_bean.get('orderDirection', 0),
                price=trade_bean.get('orderPrice', '0'),
                quantity=trade_bean.get('orderQuantity', '0'),
                status='pending',
                created_at=current_time,
                updated_at=current_time,
                metadata=json.dumps(trade_bean),
                version=0  # 初始版本
            )
            
            # 使用原子事务确保缓存和数据库一致性
            with AtomicTransaction(self) as tx:
                # 更新缓存
                self._order_cache[order_id] = order
                if strategy_id not in self._active_orders_by_strategy:
                    self._active_orders_by_strategy[strategy_id] = []
                self._active_orders_by_strategy[strategy_id].append(order_id)
                
                # 持久化到数据库
                tx.conn.execute("""
                    INSERT OR REPLACE INTO orders 
                    (order_id, strategy_id, strategy_type, trading_pair, direction, 
                     price, quantity, status, created_at, updated_at, metadata, version)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    order.order_id, order.strategy_id, order.strategy_type,
                    order.trading_pair, order.direction, order.price, order.quantity,
                    order.status, order.created_at, order.updated_at, order.metadata, order.version
                ))
            
            self.logger.debug(f"📝 订单创建记录: {order_id} by {strategy_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 订单创建记录失败: {e}")
            return False
    
    def update_order_status(self, order_id: str, status: str, filled_quantity: str = None, avg_fill_price: str = None) -> bool:
        """
        更新订单状态（原子事务，带版本控制）
        
        Args:
            order_id: 订单ID
            status: 新状态
            filled_quantity: 成交数量（可选）
            avg_fill_price: 平均成交价格（可选）
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 使用原子事务和版本控制
            with AtomicTransaction(self) as tx:
                if order_id not in self._order_cache:
                    self.logger.warning(f"⚠️ 订单不存在: {order_id}")
                    return False
                
                order = self._order_cache[order_id]
                current_version = order.version
                
                # 乐观锁检查 - 验证版本是否仍然有效
                cursor = tx.conn.execute(
                    "SELECT version FROM orders WHERE order_id = ?", 
                    (order_id,)
                )
                db_row = cursor.fetchone()
                if db_row and db_row[0] != current_version:
                    raise VersionConflictError(f"订单 {order_id} 版本冲突：缓存版本={current_version}, 数据库版本={db_row[0]}")
                
                # 更新缓存
                order.status = status
                order.updated_at = time.time()
                order.version += 1  # 增加版本号
                
                if filled_quantity is not None:
                    order.filled_quantity = filled_quantity
                if avg_fill_price is not None:
                    order.avg_fill_price = avg_fill_price
                
                # 如果订单完成或取消，从活跃列表中移除
                if status in ['filled', 'cancelled']:
                    strategy_orders = self._active_orders_by_strategy.get(order.strategy_id, [])
                    if order_id in strategy_orders:
                        strategy_orders.remove(order_id)
                
                # 更新数据库（包含版本控制）
                has_qty = filled_quantity is not None
                has_price = avg_fill_price is not None
                
                if has_qty and has_price:
                    sql = """UPDATE orders SET status = ?, updated_at = ?, filled_quantity = ?, avg_fill_price = ?, version = ? WHERE order_id = ? AND version = ?"""
                    params = (status, order.updated_at, filled_quantity, avg_fill_price, order.version, order_id, current_version)
                elif has_qty:
                    sql = """UPDATE orders SET status = ?, updated_at = ?, filled_quantity = ?, version = ? WHERE order_id = ? AND version = ?"""
                    params = (status, order.updated_at, filled_quantity, order.version, order_id, current_version)
                elif has_price:
                    sql = """UPDATE orders SET status = ?, updated_at = ?, avg_fill_price = ?, version = ? WHERE order_id = ? AND version = ?"""
                    params = (status, order.updated_at, avg_fill_price, order.version, order_id, current_version)
                else:
                    sql = """UPDATE orders SET status = ?, updated_at = ?, version = ? WHERE order_id = ? AND version = ?"""
                    params = (status, order.updated_at, order.version, order_id, current_version)
                
                result = tx.conn.execute(sql, params)
                if result.rowcount == 0:
                    raise VersionConflictError(f"订单 {order_id} 更新失败，可能被其他进程修改")
            
            self.logger.debug(f"🔄 订单状态更新: {order_id} -> {status} (v{order.version})")
            return True
            
        except VersionConflictError as e:
            self.logger.warning(f"⚠️ 版本冲突: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ 订单状态更新失败: {e}")
            return False
    
    def get_strategy_orders(self, strategy_id: str, status_filter: List[str] = None) -> List[OrderRecord]:
        """
        获取策略的订单列表
        
        Args:
            strategy_id: 策略ID
            status_filter: 状态过滤器，如 ['active', 'pending']
        
        Returns:
            List[OrderRecord]: 订单列表
        """
        try:
            with self._cache_lock:
                strategy_order_ids = self._active_orders_by_strategy.get(strategy_id, [])
                orders = []
                
                for order_id in strategy_order_ids:
                    if order_id in self._order_cache:
                        order = self._order_cache[order_id]
                        if status_filter is None or order.status in status_filter:
                            orders.append(order)
                
                return orders
                
        except Exception as e:
            self.logger.error(f"❌ 获取策略订单失败: {e}")
            return []
    
    def get_all_active_orders(self) -> List[OrderRecord]:
        """获取所有活跃订单"""
        try:
            with self._cache_lock:
                return [
                    order for order in self._order_cache.values()
                    if order.status in ['pending', 'active']
                ]
        except Exception as e:
            self.logger.error(f"❌ 获取活跃订单失败: {e}")
            return []
    
    def clear_strategy_active_orders(self, strategy_id: str) -> int:
        """
        清理特定策略的活跃订单状态（标记为已撤销）
        
        Args:
            strategy_id: 策略ID
        
        Returns:
            int: 清理的订单数量
        """
        try:
            with self._cache_lock:
                # 直接使用SQL批量更新，提高效率
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.execute(
                        """UPDATE orders 
                           SET status = 'cancelled', 
                               updated_at = ?
                           WHERE strategy_id = ? 
                           AND status IN ('pending', 'active')""",
                        (time.time(), strategy_id)
                    )
                    
                    cleared_count = cursor.rowcount
                    conn.commit()
                
                # 从缓存中移除或更新这些订单
                orders_to_update = []
                for order_id, order in self._order_cache.items():
                    if (order.strategy_id == strategy_id and 
                        order.status in ['pending', 'active']):
                        orders_to_update.append(order_id)
                
                for order_id in orders_to_update:
                    if order_id in self._order_cache:
                        # 更新缓存中的订单状态
                        cached_order = self._order_cache[order_id]
                        updated_order = OrderRecord(
                            order_id=cached_order.order_id,
                            strategy_id=cached_order.strategy_id,
                            strategy_type=cached_order.strategy_type,
                            trading_pair=cached_order.trading_pair,
                            direction=cached_order.direction,
                            price=cached_order.price,
                            quantity=cached_order.quantity,
                            filled_quantity=cached_order.filled_quantity,
                            status='cancelled',
                            created_at=cached_order.created_at,
                            updated_at=time.time(),
                            avg_fill_price=cached_order.avg_fill_price
                        )
                        self._order_cache[order_id] = updated_order
            
            if cleared_count > 0:
                self.logger.info(f"🧹 [策略清理] 已将策略 {strategy_id} 的 {cleared_count} 个活跃订单标记为已撤销")
            else:
                self.logger.debug(f"🧹 [策略清理] 策略 {strategy_id} 无需清理活跃订单")
            
            return cleared_count
            
        except Exception as e:
            self.logger.error(f"❌ 清理策略活跃订单失败: {e}")
            return 0
    
    def check_order_conflicts(self, strategy_id: str, trading_pair: str, price: Decimal, tolerance: Decimal = Decimal("0.001")) -> List[Dict[str, Any]]:
        """
        检查订单冲突
        
        Args:
            strategy_id: 当前策略ID
            trading_pair: 交易对
            price: 订单价格
            tolerance: 价格容忍度
        
        Returns:
            List[Dict]: 冲突订单列表
        """
        conflicts = []
        
        try:
            with self._cache_lock:
                for order in self._order_cache.values():
                    # 跳过自己的策略和非活跃订单
                    if (order.strategy_id == strategy_id or 
                        order.status not in ['pending', 'active'] or
                        order.trading_pair != trading_pair):
                        continue
                    
                    order_price = Decimal(order.price)
                    price_diff = abs(order_price - price) / price
                    
                    if price_diff <= tolerance:
                        conflicts.append({
                            'order_id': order.order_id,
                            'strategy_id': order.strategy_id,
                            'strategy_type': order.strategy_type,
                            'price': order.price,
                            'price_diff': float(price_diff),
                            'direction': order.direction
                        })
            
            return conflicts
            
        except Exception as e:
            self.logger.error(f"❌ 冲突检查失败: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            with self._cache_lock:
                stats = {
                    'total_orders': len(self._order_cache),
                    'active_orders': len([o for o in self._order_cache.values() if o.status in ['pending', 'active']]),
                    'strategies': len(self._strategy_cache),
                    'by_strategy': {},
                    'by_status': {}
                }
                
                # 按策略统计
                for strategy_id, orders in self._active_orders_by_strategy.items():
                    stats['by_strategy'][strategy_id] = len(orders)
                
                # 按状态统计
                for order in self._order_cache.values():
                    status = order.status
                    if status not in stats['by_status']:
                        stats['by_status'][status] = 0
                    stats['by_status'][status] += 1
                
                return stats
                
        except Exception as e:
            self.logger.error(f"❌ 统计信息获取失败: {e}")
            return {}
    
    def cleanup_old_orders(self, max_age_hours: int = 24) -> int:
        """
        清理旧订单记录
        
        Args:
            max_age_hours: 最大保留时间（小时）
        
        Returns:
            int: 清理的订单数量
        """
        try:
            cutoff_time = time.time() - (max_age_hours * 3600)
            cleaned_count = 0
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    DELETE FROM orders 
                    WHERE status IN ('filled', 'cancelled') 
                    AND updated_at < ?
                """, (cutoff_time,))
                cleaned_count = cursor.rowcount
                conn.commit()
            
            # 重新加载缓存
            self._load_cache()
            
            self.logger.info(f"🧹 清理完成: 删除 {cleaned_count} 个旧订单")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"❌ 清理失败: {e}")
            return 0
    
    def batch_update_orders(self, updates: List[tuple]) -> bool:
        """
        批量更新订单状态（高性能）
        
        Args:
            updates: [(order_id, status, filled_quantity, avg_fill_price), ...]
        
        Returns:
            bool: 批量更新是否成功
        """
        if not updates:
            return True
            
        try:
            with AtomicTransaction(self) as tx:
                for update_data in updates:
                    order_id = update_data[0]
                    status = update_data[1]
                    filled_quantity = update_data[2] if len(update_data) > 2 else None
                    avg_fill_price = update_data[3] if len(update_data) > 3 else None
                    
                    if order_id not in self._order_cache:
                        continue
                    
                    order = self._order_cache[order_id]
                    order.status = status
                    order.updated_at = time.time()
                    order.version += 1
                    
                    if filled_quantity is not None:
                        order.filled_quantity = filled_quantity
                    if avg_fill_price is not None:
                        order.avg_fill_price = avg_fill_price
                
                # 批量更新数据库
                update_params = []
                for update_data in updates:
                    order_id = update_data[0]
                    if order_id in self._order_cache:
                        order = self._order_cache[order_id]
                        update_params.append((
                            order.status, order.updated_at, order.filled_quantity, 
                            order.avg_fill_price, order.version, order_id
                        ))
                
                if update_params:
                    tx.conn.executemany("""
                        UPDATE orders SET status=?, updated_at=?, filled_quantity=?, 
                                        avg_fill_price=?, version=? WHERE order_id=?
                    """, update_params)
            
            self.logger.debug(f"🔄 批量更新 {len(updates)} 个订单状态")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 批量更新失败: {e}")
            return False
    
    def get_strategy_statistics(self, strategy_id: str) -> Dict[str, Any]:
        """
        获取策略统计信息（只读操作，无锁优化）
        
        Args:
            strategy_id: 策略ID
        
        Returns:
            Dict: 统计信息
        """
        try:
            # 只读操作，使用轻量级锁
            orders = [o for o in self._order_cache.values() 
                     if o.strategy_id == strategy_id]
            
            stats = {
                'total_orders': len(orders),
                'active_orders': len([o for o in orders if o.status in ['pending', 'active']]),
                'filled_orders': len([o for o in orders if o.status == 'filled']),
                'cancelled_orders': len([o for o in orders if o.status == 'cancelled']),
                'total_volume': sum(float(o.quantity) for o in orders if o.status == 'filled'),
                'avg_price': 0.0
            }
            
            filled_orders = [o for o in orders if o.status == 'filled' and float(o.avg_fill_price) > 0]
            if filled_orders:
                stats['avg_price'] = sum(float(o.avg_fill_price) for o in filled_orders) / len(filled_orders)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"❌ 统计信息获取失败: {e}")
            return {}
    
    def cleanup_inactive_strategies(self) -> int:
        """
        清理非活跃策略的历史数据
        
        Returns:
            int: 清理的策略数量
        """
        try:
            cleaned_count = 0
            
            with AtomicTransaction(self) as tx:
                # 查找没有活跃订单的策略
                cursor = tx.conn.execute("""
                    SELECT strategy_id FROM strategy_metadata 
                    WHERE strategy_id NOT IN (
                        SELECT DISTINCT strategy_id FROM orders 
                        WHERE status IN ('pending', 'active')
                    )
                """)
                
                inactive_strategies = [row[0] for row in cursor.fetchall()]
                
                for strategy_id in inactive_strategies:
                    # 从缓存中移除
                    if strategy_id in self._strategy_cache:
                        del self._strategy_cache[strategy_id]
                    if strategy_id in self._active_orders_by_strategy:
                        del self._active_orders_by_strategy[strategy_id]
                    
                    # 从数据库中删除
                    tx.conn.execute(
                        "DELETE FROM strategy_metadata WHERE strategy_id = ?", 
                        (strategy_id,)
                    )
                    cleaned_count += 1
            
            self.logger.info(f"🧹 清理 {cleaned_count} 个非活跃策略")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"❌ 策略清理失败: {e}")
            return 0
    
    def close(self):
        """关闭OMS服务，清理资源"""
        try:
            if hasattr(self, 'connection_pool'):
                self.connection_pool.close_all()
            self.logger.info("✅ OrderManagementService已关闭")
        except Exception as e:
            self.logger.error(f"❌ OMS关闭失败: {e}")


# 全局单例实例
_oms_instance: Optional[OrderManagementService] = None
_oms_lock = threading.Lock()


def get_oms() -> OrderManagementService:
    """获取OMS单例实例"""
    global _oms_instance
    
    if _oms_instance is None:
        with _oms_lock:
            if _oms_instance is None:
                _oms_instance = OrderManagementService()
    
    return _oms_instance


def init_oms(db_path: str = None) -> OrderManagementService:
    """初始化OMS实例"""
    global _oms_instance
    
    with _oms_lock:
        if db_path:
            _oms_instance = OrderManagementService(db_path)
        else:
            _oms_instance = OrderManagementService()
    
    return _oms_instance