# app/services/order_status_monitor.py

from .nine_client import NineClient
from .order_management_service import get_oms, generate_stable_strategy_id
from decimal import Decimal
from typing import List, Dict, Optional, Any
import logging


class OrderStatusMonitor:
    """订单状态监控器 - 专门负责订单状态的查询和更新"""
    
    def __init__(self, nine_client: NineClient, strategy, logger: logging.Logger):
        """
        初始化订单状态监控器
        
        Args:
            nine_client: Nine CEX客户端
            strategy: 交易策略实例
            logger: 日志记录器
        """
        self.nine_client = nine_client
        self.strategy = strategy
        # 使用传入的logger但禁用传播，避免重复日志
        self.logger = logger
        self.logger.propagate = False
        self.oms = get_oms()
        
        # 生成策略ID
        self.strategy_id = generate_stable_strategy_id(strategy)
    
    def get_orders_from_oms(self) -> List[Dict]:
        """
        从OMS获取当前策略的活跃订单
        
        Returns:
            List[Dict]: 当前策略的活跃订单列表
        """
        try:
            oms_orders = self.oms.get_strategy_orders(
                self.strategy_id, 
                status_filter=['pending', 'active']
            )
            
            # 转换为策略期望的格式
            formatted_orders = []
            for order in oms_orders:
                formatted_orders.append({
                    'orderId': order.order_id,
                    'price': Decimal(order.price),
                    'original_quantity': Decimal(order.quantity),
                    'direction': order.direction,
                    'trading_pair': order.trading_pair,
                    'status': order.status,
                    'quantity': order.quantity
                })
            
            self.logger.debug(f"📋 [OMS查询] 获取策略 {self.strategy_id} 的 {len(formatted_orders)} 个活跃订单")
            return formatted_orders
            
        except Exception as e:
            self.logger.error(f"❌ [OMS查询] 获取订单失败: {e}")
            return []
    
    def get_real_active_orders(self) -> List[Dict]:
        """
        获取当前所有活跃订单（支持OMS数据源和API数据源）
        
        Returns:
            List[Dict]: 当前实际活跃的订单列表
        """
        # 优先使用OMS数据（更快，更准确）
        try:
            oms_orders = self.get_orders_from_oms()
            # 修复：即使OMS返回空列表也是有效结果（如首次运行的策略）
            # 只有在查询异常时才降级到API查询
            self.logger.debug(f"🎯 [OMS查询] 使用OMS数据: {len(oms_orders)}个本策略订单")
            return oms_orders
        except Exception as e:
            self.logger.warning(f"⚠️ [混合查询] OMS查询失败，降级到API查询: {e}")
        
        # 降级到API查询（获取该交易对的所有订单，无法区分策略）
        return self.get_real_active_orders_from_api()
    
    def get_real_active_orders_from_api(self) -> List[Dict]:
        """
        从Nine API实时获取当前所有活跃订单
        
        Returns:
            List[Dict]: 当前实际活跃的订单列表
        """
        if not self.strategy.api_key or not self.strategy.api_secret:
            self.logger.warning("⚠️ [API查询] 缺少API凭证，无法查询真实订单状态")
            return []
            
        try:
            # 获取策略的交易对名称
            trade_pair_name = getattr(self.strategy, 'trading_pair', None)
            if not trade_pair_name:
                self.logger.warning("⚠️ [API查询] 策略未设置trading_pair，无法查询订单")
                return []
                
            # 调用Nine API获取当前订单 - 获取所有页的订单后本地过滤
            result = self.nine_client.get_current_orders(
                trade_pair=trade_pair_name,
                user_api_key=self.strategy.api_key,
                user_api_secret=self.strategy.api_secret,
                page_size=500,  # 单页获取更多订单以减少请求次数
                page_num=1,
                get_all_pages=True  # 获取所有页确保不遗漏订单
            )
            
            # 检查API调用结果
            if not result or not isinstance(result, dict) or result.get("code") != 200:
                self.logger.error(f"❌ [API查询] API调用失败: {result}")
                return []
                
            order_list = result.get("data", {}).get("list", [])
            total_count = result.get("data", {}).get("total", 0)
            
            # 转换为与本地格式一致的数据结构
            real_orders = []
            for order in order_list:
                # 只处理挂单中的订单（orderStatus == 0）
                if order.get("orderStatus") == 0:  # 0=挂单中
                    real_orders.append({
                        'orderId': order.get('orderId'),
                        'price': Decimal(str(order.get('orderPrice', '0'))),
                        'original_quantity': Decimal(str(order.get('quantity', '0'))),
                        'direction': order.get('orderDirection'),  # 1=买入, 2=卖出
                        'quantity': order.get('quantity', '0'),
                        'tradePair': order.get('tradePair', ''),  # 添加交易对信息
                        'symbol': order.get('symbol', '')  # 添加symbol信息
                    })
                    
            self.logger.info(f"🎯 [API查询] 成功获取 {trade_pair_name} 的 {len(real_orders)} 个挂单中的订单")
            return real_orders
            
        except Exception as e:
            self.logger.error(f"❌ [API查询] 查询异常: {e}")
            return []
    
    def check_trade_execution(self, order_ids: List[str]) -> None:
        """
        检查订单成交情况（高效模式）
        
        Args:
            order_ids: 要检查的订单ID列表
        """
        if not self.strategy.api_key or not self.strategy.api_secret:
            self.logger.error("无法查询成交记录: API Key 或 Secret 未配置")
            return

        if not order_ids:
            return
        
        self.logger.info(f"检查 {len(order_ids)} 个订单状态")
        
        executed_orders = []
        failed_queries = []
        partial_filled_orders = []
        pending_orders = []
        not_found_orders = []
        
        # 逐个查询每个订单的详细状态
        for order_id in order_ids:
            try:
                order_detail = self.nine_client.get_order_detail_by_id(
                    order_id=order_id,
                    user_api_key=self.strategy.api_key,
                    user_api_secret=self.strategy.api_secret
                )
                
                if not order_detail:
                    failed_queries.append(order_id)
                    continue
                
                # 检查查询是否成功
                if not order_detail.get("query_success", False):
                    reason = order_detail.get("reason", "unknown")
                    if "connection_error" in reason or "api_error" in reason:
                        failed_queries.append(order_id)
                        self.logger.warning(f"⚠️ [NINE CEX] 订单 {order_id} 查询失败: {reason}")
                    elif "order_not_found" in reason:
                        not_found_orders.append(order_id)
                        self.logger.warning(f"📭 [NINE CEX] 订单 {order_id} 不存在，可能已被手动删除")
                    continue
                
                # 检查订单是否存在
                if not order_detail.get("exists", False):
                    not_found_orders.append(order_id)
                    continue
                
                # 解析订单状态
                order_status = order_detail.get("orderStatus", 0)
                filled_quantity = order_detail.get("filledQuantity", "0")
                progress = order_detail.get("progress", "0")
                
                # 判断订单状态
                if order_status == 1 and progress == "100":
                    # 完全成交
                    executed_orders.append({
                        'orderId': order_id,
                        'orderStatus': order_status,
                        'filledQuantity': filled_quantity,
                        'progress': progress,
                        'orderDirection': order_detail.get("orderDirection", 0),
                        'orderPrice': order_detail.get("orderPrice", "N/A"),
                        'filledAmount': order_detail.get("filledAmount", "N/A"),
                        'totalFee': order_detail.get("totalFee", "N/A"),
                        'orderTime': order_detail.get("orderTime", "N/A"),
                        'avgPrice': order_detail.get("avgPrice", "0")
                    })
                    # 更新OMS状态为已成交
                    self.oms.update_order_status(
                        order_id, 
                        'filled',
                        filled_quantity=filled_quantity,
                        avg_fill_price=order_detail.get("avgPrice", "0")
                    )
                    self.logger.info(f"✅ [NINE CEX] 订单 {order_id} 已完全成交 (进度: {progress}%)")
                elif order_status == 1 and progress != "100":
                    # 部分成交
                    partial_filled_orders.append({
                        'orderId': order_id,
                        'progress': progress,
                        'filledQuantity': filled_quantity,
                        'orderDirection': order_detail.get("orderDirection", 0)
                    })
                    # 更新OMS状态为活跃（部分成交）
                    self.oms.update_order_status(
                        order_id,
                        'active',
                        filled_quantity=filled_quantity,
                        avg_fill_price=order_detail.get("avgPrice", "0")
                    )
                    self.logger.info(f"🔄 [NINE CEX] 订单 {order_id} 部分成交 (进度: {progress}%)")
                elif order_status == 0:
                    # 挂单中
                    pending_orders.append(order_id)
                    # 更新OMS状态为活跃
                    self.oms.update_order_status(order_id, 'active')
                elif order_status == 2:
                    # 已撤销
                    not_found_orders.append(order_id)
                    # 更新OMS状态为已撤销
                    self.oms.update_order_status(order_id, 'cancelled')
                    self.logger.info(f"❌ [NINE CEX] 订单 {order_id} 已撤销")
                else:
                    # 未知状态 - 通常表示订单已失效，更新为已撤销
                    self.logger.warning(f"❓ [NINE CEX] 订单 {order_id} 状态未知: {order_status}，标记为失效")
                    not_found_orders.append(order_id)
                    # 更新OMS状态为已撤销
                    self.oms.update_order_status(order_id, 'cancelled')
                    
            except Exception as e:
                self.logger.warning(f"⚠️ [NINE CEX] 查询订单 {order_id} 状态异常: {e}")
                failed_queries.append(order_id)
        
        # 统计查询结果
        self._log_execution_results(
            total_checked=len(order_ids),
            executed_orders=executed_orders,
            partial_filled_orders=partial_filled_orders,
            pending_orders=pending_orders,
            not_found_orders=not_found_orders,
            failed_queries=failed_queries
        )
    
    def _log_execution_results(self, total_checked: int, executed_orders: List[Dict], 
                              partial_filled_orders: List[Dict], pending_orders: List[str],
                              not_found_orders: List[str], failed_queries: List[str]):
        """记录执行结果统计"""
        successful_queries = total_checked - len(failed_queries)
        executed_count = len(executed_orders)
        
        self.logger.info(f"订单查询完成: {successful_queries}/{total_checked}")
        
        if executed_count > 0:
            self.logger.info(f"完全成交: {executed_count} 个")
            for exec_order in executed_orders:
                direction_text = "买入" if exec_order['orderDirection'] == 1 else "卖出"
                avg_price = exec_order['avgPrice'] if exec_order['avgPrice'] != "0" else exec_order['orderPrice']
                self.logger.info(f"成交: {direction_text} {exec_order['filledQuantity']} @ {avg_price} | "
                               f"ID: {exec_order['orderId']} | 金额: {exec_order['filledAmount']}")
        
        if len(partial_filled_orders) > 0:
            self.logger.info(f"部分成交: {len(partial_filled_orders)} 个")
            for partial_order in partial_filled_orders:
                direction_text = "买入" if partial_order['orderDirection'] == 1 else "卖出"
                self.logger.info(f"部分成交: {direction_text} {partial_order['filledQuantity']} ({partial_order['progress']}%)")
        
        if len(pending_orders) > 0:
            self.logger.info(f"挂单中: {len(pending_orders)} 个")
        
        if len(not_found_orders) > 0:
            self.logger.info(f"不存在/已撤销: {len(not_found_orders)} 个")
        
        if len(failed_queries) > 0:
            self.logger.warning(f"查询失败: {len(failed_queries)} 个")
    
    def get_trading_pair_for_query(self) -> Optional[str]:
        """获取用于查询的交易对名称"""
        # 先尝试策略实例的属性
        if hasattr(self.strategy, 'trading_pair'):
            return self.strategy.trading_pair
        elif hasattr(self.strategy, 'fetch_nine_book_symbol'):
            return self.strategy.fetch_nine_book_symbol
        elif hasattr(self.strategy, 'trade_pair'):
            return self.strategy.trade_pair
        
        return None