# Nine Trade Maker

数字货币交易机器人，支持多种交易策略和Nine CEX交易所。

## 安装

### 环境要求
- Python 3.8+
- pip

### 快速安装

#### 自动安装（推荐）
```bash
# 运行自动配置脚本
./setup.sh
```

#### 手动安装
```bash
# 创建虚拟环境
python -m venv nine-trade-env

# 激活虚拟环境
source nine-trade-env/bin/activate  # Linux/macOS
# nine-trade-env\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp env.example .env
# 编辑.env文件，填入API密钥
```

## 策略启动

### 基本命令
```bash
# 启动策略（后台运行）
flask start-trading-bot --strategy [策略名]

# 停止策略
flask stop-trading-bot

# 单次运行
flask run-bot-once --strategy [策略名]

# 模拟运行（不实际下单）
flask run-bot-once --strategy [策略名] --dry-run
```

### 启动示例
```bash
# 简单活跃策略
flask start-trading-bot --strategy active

# 自适应K线策略
flask start-trading-bot --strategy adaptive_kline

# CEX价格平衡策略
flask start-trading-bot --strategy cex_price_balance

# 跨交易所套利策略
flask start-trading-bot --strategy cross_exchange_arbitrage

# 累积深度策略
flask start-trading-bot --strategy cumulative_depth

# 增强流动性提供策略
flask start-trading-bot --strategy enhanced_liquidity_provider

# 梯度流动性策略
flask start-trading-bot --strategy gradient_liquidity

# 流动性提供策略
flask start-trading-bot --strategy liquidity_provider

# 币安镜像策略
flask start-trading-bot --strategy mirror_binance

# 价格镜像策略
flask start-trading-bot --strategy mirror_price

# Raydium价格平衡策略
flask start-trading-bot --strategy raydium_price_balance

# 成交量K线策略
flask start-trading-bot --strategy volume_kline

# 简单活跃策略（制造盘口活跃度）
flask start-trading-bot --strategy active
```

## 订单管理

### 清理策略订单
```bash
# 清理指定策略的所有订单
flask clean-strategy-orders --strategy [策略名]

# 清理所有策略的订单
flask clean-strategy-orders --all-strategies

# 清理指定交易对的订单
flask clean-strategy-orders --strategy [策略名] --trading-pair BTC/USDT

# 清理CEX上所有指定交易对的订单（不限策略）
flask clean-strategy-orders --all-cex-orders --trading-pair SOL/USDT

# 预览模式（查看但不执行）
flask clean-strategy-orders --strategy [策略名] --dry-run

# 仅清理本地缓存
flask clean-strategy-orders --strategy [策略名] --local-only

# 仅撤销CEX订单
flask clean-strategy-orders --strategy [策略名] --cex-only

# 强制清理（跳过确认）
flask clean-strategy-orders --strategy [策略名] --force
```

### 清理示例
```bash
# 清理简单活跃策略的所有订单
flask clean-strategy-orders --strategy active

# 清理CEX价格平衡策略的所有订单
flask clean-strategy-orders --strategy cex_price_balance

# 清理所有策略的订单（本地缓存 + CEX订单）
flask clean-strategy-orders --all-strategies

# 清理DOGE/USDT交易对的订单
flask clean-strategy-orders --strategy cex_price_balance --trading-pair DOGE/USDT

# 清理CEX上所有SOL/USDT订单（不管属于哪个策略）
flask clean-strategy-orders --all-cex-orders --trading-pair SOL/USDT

# 预览将要清理的订单
flask clean-strategy-orders --strategy cex_price_balance --dry-run

# 预览所有策略的订单清理
flask clean-strategy-orders --all-strategies --dry-run
```

## 可用策略

| 策略名称 | 描述 |
|---------|------|
| active | 简单活跃策略 |
| adaptive_kline | 自适应K线策略 |
| cex_price_balance | CEX价格平衡策略 |
| cross_exchange_arbitrage | 跨交易所套利策略 |
| cumulative_depth | 累积深度策略 |
| enhanced_liquidity_provider | 增强流动性提供策略 |
| gradient_liquidity | 梯度流动性策略 |
| liquidity_provider | 流动性提供策略 |
| mirror_binance | 币安镜像策略 |
| mirror_price | 价格镜像策略 |
| raydium_price_balance | Raydium价格平衡策略 |
| volume_kline | 成交量K线策略 |

## 配置文件

### 主配置
- `.env` - 主配置文件，包含API密钥等基础配置

### 策略配置
- `config/strategies/active.env` - 简单活跃策略（制造盘口活跃度）
- `config/strategies/adaptive_kline.env`
- `config/strategies/cex_price_balance.env`
- `config/strategies/cross_exchange_arbitrage.env`
- `config/strategies/cumulative_depth.env`
- `config/strategies/enhanced_liquidity_provider.env`
- `config/strategies/gradient_liquidity.env`
- `config/strategies/liquidity_provider.env`
- `config/strategies/mirror_binance.env`
- `config/strategies/mirror_price.env`
- `config/strategies/raydium_price_balance.env`
- `config/strategies/volume_kline.env`

## 其他命令

### 初始化流动性基线
```bash
flask init-liquidity-baseline
flask init-liquidity-baseline --force
```

### Web API
```bash
flask run --host=0.0.0.0 --port=5000
```

## 注意事项

1. 确保`.env`文件中配置了正确的API密钥
2. 生产环境使用前请先在模拟模式测试
3. 策略异常退出后使用清理命令清除残留订单
4. 定期检查日志文件排查问题