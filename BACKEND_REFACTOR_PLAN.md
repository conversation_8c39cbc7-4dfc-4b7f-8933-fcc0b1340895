# Nine Trade Maker 后端化改造计划

## 一、现状分析

### 1.1 当前架构评估

#### 存在的问题

1. **单实例限制**
   - `TradingBotService` 使用阻塞式运行（`while self.is_running`）
   - 配置在启动时固定，无法动态修改
   - 一个进程只能运行一个策略实例
   - CLI 命令启动方式不适合后台服务

2. **配置管理局限**
   - `ConfigLoader` 依赖文件系统
   - 环境变量全局共享，无法实例隔离
   - 缺少配置验证和类型检查
   - 策略配置变更需要重启整个系统

3. **OMS 并发问题**
   - 虽然支持多策略，但缺少实例级别的隔离
   - SQLite 在高并发下可能出现锁竞争
   - 缺少实例级别的性能监控
   - 策略间可能存在订单冲突

4. **日志系统不足**
   - 所有实例共享同一日志流
   - 难以追踪特定实例的问题
   - 缺少结构化日志
   - 无法按实例进行日志分析

### 1.2 改造优先级

- **P0（必须）**：策略运行模式改造、配置隔离
- **P1（重要）**：OMS 多实例支持、日志隔离
- **P2（优化）**：监控系统、性能优化

### 1.3 技术债务识别

1. **代码架构债务**
   - 单体应用设计，缺少模块化
   - 紧耦合的组件设计
   - 缺少依赖注入机制

2. **测试债务**
   - 单元测试覆盖率不足
   - 缺少集成测试
   - 无自动化测试流程

3. **文档债务**
   - API 文档不完整
   - 缺少架构设计文档
   - 部署和运维文档缺失

## 二、改造目标

### 2.1 核心目标

- ✅ 支持同时运行 100+ 策略实例
- ✅ 每个实例独立配置和管理
- ✅ 提供 RESTful API 进行管理
- ✅ 实现 Web UI 可视化管理
- ✅ 保证系统稳定性和可恢复性

### 2.2 质量目标

- 代码测试覆盖率 > 80%
- API 响应时间 < 200ms (P95)
- 系统可用性 > 99.9%
- 支持优雅升级和回滚
- 内存使用稳定，无泄漏

### 2.3 非功能性需求

- **可扩展性**：支持水平扩展到多节点
- **可观测性**：完整的监控、日志、追踪
- **安全性**：API 认证、配置加密、审计日志
- **易用性**：直观的 Web 界面、完整的文档

## 三、技术改造方案

### 3.1 策略运行模式改造

#### 改造前（当前代码）
```python
# app/services/market_maker_service.py
class TradingBotService:
    def run(self):
        while self.is_running:
            self.strategy.execute()
            time.sleep(self.update_interval)
```

#### 改造后（支持后台运行）
```python
# app/services/market_maker_service.py
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Optional, Dict, Any
from dataclasses import dataclass
import time
from datetime import datetime

@dataclass
class InstanceContext:
    """实例运行上下文"""
    instance_id: str
    strategy_type: str
    config: Dict[str, Any]
    logger: logging.Logger
    oms_client: Any
    
class TradingBotService:
    """改造后的交易机器人服务 - 支持多实例管理"""
    
    def __init__(self, context: InstanceContext, nine_client, strategy, config, logger, binance_client=None):
        # 继承原有初始化逻辑
        self.context = context
        self.instance_id = context.instance_id
        self.nine_client = nine_client
        self.strategy = strategy
        self.config = config
        self.logger = context.logger
        self.binance_client = binance_client
        
        # 新增的运行控制
        self._thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._pause_event = threading.Event()
        self._health_check_event = threading.Event()
        self._last_heartbeat = time.time()
        self._error_count = 0
        self._max_errors = 5
        
        # 性能监控
        self.metrics = {
            'start_time': None,
            'execution_count': 0,
            'error_count': 0,
            'last_execution_time': None,
            'avg_execution_duration': 0.0
        }
        
    def start_background(self) -> bool:
        """在后台线程启动策略"""
        if self._thread and self._thread.is_alive():
            self.logger.warning(f"实例 {self.instance_id} 已在运行中")
            return False
            
        self.logger.info(f"启动策略实例: {self.instance_id}")
        self._stop_event.clear()
        self._pause_event.clear()
        self.metrics['start_time'] = datetime.now()
        
        self._thread = threading.Thread(
            target=self._run_loop,
            name=f"strategy-{self.instance_id}",
            daemon=True
        )
        self._thread.start()
        return True
        
    def _run_loop(self):
        """策略执行循环（在独立线程中）"""
        self.logger.info(f"策略执行循环开始: {self.instance_id}")
        
        while not self._stop_event.is_set():
            if self._pause_event.is_set():
                self.logger.debug(f"策略已暂停: {self.instance_id}")
                self._stop_event.wait(1)
                continue
                
            try:
                # 更新心跳
                self._last_heartbeat = time.time()
                self._health_check_event.set()
                
                # 执行策略
                start_time = time.time()
                self.strategy.execute()
                execution_duration = time.time() - start_time
                
                # 更新指标
                self._update_metrics(execution_duration)
                self._error_count = 0  # 成功执行后重置错误计数
                
                # 记录成功执行
                self.logger.debug(f"策略执行完成: {self.instance_id}, 耗时: {execution_duration:.2f}s")
                
            except Exception as e:
                self._handle_execution_error(e)
                
            # 等待下次执行
            self._stop_event.wait(self.update_interval)
            
        self.logger.info(f"策略执行循环结束: {self.instance_id}")
    
    def _update_metrics(self, execution_duration: float):
        """更新性能指标"""
        self.metrics['execution_count'] += 1
        self.metrics['last_execution_time'] = time.time()
        
        # 计算平均执行时间
        count = self.metrics['execution_count']
        current_avg = self.metrics['avg_execution_duration']
        self.metrics['avg_execution_duration'] = (current_avg * (count - 1) + execution_duration) / count
    
    def _handle_execution_error(self, error: Exception):
        """处理执行错误"""
        self._error_count += 1
        self.metrics['error_count'] += 1
        
        self.logger.error(f"策略执行异常: {self.instance_id}, 错误: {str(error)}, 累计错误: {self._error_count}")
        
        if self._error_count >= self._max_errors:
            self.logger.critical(f"策略实例错误次数过多，自动停止: {self.instance_id}")
            self._stop_event.set()
        else:
            # 指数退避重试
            backoff_time = min(60, 2 ** self._error_count)
            self.logger.warning(f"将在 {backoff_time} 秒后重试")
            self._stop_event.wait(backoff_time)
    
    def stop(self, timeout: float = 10) -> bool:
        """优雅停止"""
        if not self._thread or not self._thread.is_alive():
            return True
            
        self.logger.info(f"停止策略实例: {self.instance_id}")
        self._stop_event.set()
        self._thread.join(timeout)
        
        is_stopped = not self._thread.is_alive()
        if is_stopped:
            self.logger.info(f"策略实例已停止: {self.instance_id}")
        else:
            self.logger.warning(f"策略实例停止超时: {self.instance_id}")
            
        return is_stopped
    
    def pause(self) -> bool:
        """暂停策略执行"""
        if not self.is_running():
            return False
            
        self.logger.info(f"暂停策略实例: {self.instance_id}")
        self._pause_event.set()
        return True
    
    def resume(self) -> bool:
        """恢复策略执行"""
        if not self.is_running():
            return False
            
        self.logger.info(f"恢复策略实例: {self.instance_id}")
        self._pause_event.clear()
        return True
    
    def is_running(self) -> bool:
        """检查是否在运行"""
        return self._thread and self._thread.is_alive() and not self._stop_event.is_set()
    
    def is_paused(self) -> bool:
        """检查是否已暂停"""
        return self._pause_event.is_set()
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        current_time = time.time()
        return {
            'instance_id': self.instance_id,
            'is_running': self.is_running(),
            'is_paused': self.is_paused(),
            'last_heartbeat': self._last_heartbeat,
            'heartbeat_age': current_time - self._last_heartbeat,
            'error_count': self._error_count,
            'metrics': self.metrics.copy(),
            'thread_alive': self._thread.is_alive() if self._thread else False
        }
```

### 3.2 配置系统改造

#### 新增配置管理器
```python
# app/services/config_manager.py
from typing import Dict, Any, Optional, List
import jsonschema
from dataclasses import dataclass, field
import json
import hashlib
from datetime import datetime
from cryptography.fernet import Fernet
import base64
import os

@dataclass
class InstanceConfig:
    """实例配置"""
    instance_id: str
    strategy_type: str
    api_credentials: Dict[str, str]  # 加密存储
    strategy_params: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'instance_id': self.instance_id,
            'strategy_type': self.strategy_type,
            'api_credentials': self.api_credentials,
            'strategy_params': self.strategy_params,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version
        }

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self._schemas = self._load_schemas()
    
    def _load_schemas(self) -> Dict[str, dict]:
        """加载配置验证 schemas"""
        schemas = {
            'adaptive_kline': {
                "type": "object",
                "properties": {
                    "AK_TRADING_PAIR": {"type": "string", "pattern": "^[A-Z]+/[A-Z]+$"},
                    "AK_MODE": {"type": "string", "enum": ["bull", "bear", "sideways"]},
                    "AK_ORDER_VALUE": {"type": "number", "minimum": 0.1, "maximum": 10000},
                    "AK_BASE_INTERVAL": {"type": "integer", "minimum": 5, "maximum": 300},
                    "AK_PRICE_PRECISION": {"type": "integer", "minimum": 0, "maximum": 8},
                    "AK_QTY_PRECISION": {"type": "integer", "minimum": 0, "maximum": 8}
                },
                "required": ["AK_TRADING_PAIR", "AK_MODE", "AK_ORDER_VALUE"]
            },
            'mirror_binance': {
                "type": "object",
                "properties": {
                    "MM_BINANCE_SYMBOL": {"type": "string"},
                    "MM_NINE_CEX_SYMBOL": {"type": "string"},
                    "MM_SPREAD_PERCENTAGE": {"type": "number", "minimum": 0.0001, "maximum": 0.1},
                    "MM_UPDATE_INTERVAL": {"type": "integer", "minimum": 10, "maximum": 300}
                },
                "required": ["MM_BINANCE_SYMBOL", "MM_NINE_CEX_SYMBOL"]
            }
            # 其他策略的 schema...
        }
        return schemas
    
    def validate(self, strategy_type: str, config: Dict[str, Any]) -> List[str]:
        """验证配置，返回错误列表"""
        errors = []
        
        # 基础验证
        if not isinstance(config, dict):
            errors.append("配置必须是字典格式")
            return errors
        
        # API 凭证验证
        if not config.get('NINE_API_KEY'):
            errors.append("缺少 NINE_API_KEY")
        elif len(config['NINE_API_KEY']) < 10:
            errors.append("NINE_API_KEY 格式不正确")
            
        if not config.get('NINE_API_SECRET'):
            errors.append("缺少 NINE_API_SECRET")
        elif len(config['NINE_API_SECRET']) < 10:
            errors.append("NINE_API_SECRET 格式不正确")
        
        # 策略特定验证
        schema = self._schemas.get(strategy_type)
        if schema:
            try:
                jsonschema.validate(config, schema)
            except jsonschema.ValidationError as e:
                errors.append(f"配置验证失败: {e.message}")
                
        return errors

class ConfigEncryption:
    """配置加密器"""
    
    def __init__(self):
        self.key = self._get_encryption_key()
        self.cipher = Fernet(self.key)
    
    def _get_encryption_key(self) -> bytes:
        """获取或生成加密密钥"""
        key_file = 'config/.encryption_key'
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # 生成新密钥
            key = Fernet.generate_key()
            os.makedirs(os.path.dirname(key_file), exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def encrypt_credentials(self, credentials: Dict[str, str]) -> Dict[str, str]:
        """加密 API 凭证"""
        encrypted = {}
        for key, value in credentials.items():
            if value:  # 只加密非空值
                encrypted_value = self.cipher.encrypt(value.encode())
                encrypted[key] = base64.b64encode(encrypted_value).decode()
            else:
                encrypted[key] = value
        return encrypted
    
    def decrypt_credentials(self, encrypted_credentials: Dict[str, str]) -> Dict[str, str]:
        """解密 API 凭证"""
        decrypted = {}
        for key, value in encrypted_credentials.items():
            if value and len(value) > 10:  # 假设是加密的
                try:
                    encrypted_bytes = base64.b64decode(value.encode())
                    decrypted_value = self.cipher.decrypt(encrypted_bytes).decode()
                    decrypted[key] = decrypted_value
                except:
                    # 解密失败，可能是明文
                    decrypted[key] = value
            else:
                decrypted[key] = value
        return decrypted

class ConfigManager:
    """配置管理器 - 支持动态配置和验证"""
    
    def __init__(self):
        self._configs: Dict[str, InstanceConfig] = {}
        self._validator = ConfigValidator()
        self._encryption = ConfigEncryption()
        self._lock = threading.RLock()
        
    def create_config(self, strategy_type: str, params: Dict[str, Any]) -> InstanceConfig:
        """创建并验证配置"""
        with self._lock:
            # 验证配置
            errors = self._validator.validate(strategy_type, params)
            if errors:
                raise ValueError(f"配置验证失败: {', '.join(errors)}")
            
            # 分离 API 凭证和策略参数
            api_credentials = {
                'api_key': params.pop('NINE_API_KEY', ''),
                'api_secret': params.pop('NINE_API_SECRET', ''),
                'binance_api_key': params.pop('BINANCE_API_KEY', ''),
                'binance_api_secret': params.pop('BINANCE_API_SECRET', '')
            }
            
            # 加密 API 凭证
            encrypted_credentials = self._encryption.encrypt_credentials(api_credentials)
            
            # 生成实例 ID
            instance_id = self._generate_instance_id(strategy_type, params)
            
            # 创建实例配置
            config = InstanceConfig(
                instance_id=instance_id,
                strategy_type=strategy_type,
                api_credentials=encrypted_credentials,
                strategy_params=params
            )
            
            self._configs[instance_id] = config
            return config
    
    def _generate_instance_id(self, strategy_type: str, params: Dict[str, Any]) -> str:
        """生成唯一实例 ID"""
        # 使用策略类型和关键参数生成稳定的 ID
        key_params = {
            'strategy_type': strategy_type,
            'trading_pair': params.get('AK_TRADING_PAIR') or params.get('MM_NINE_CEX_SYMBOL', ''),
            'timestamp': datetime.now().isoformat()
        }
        
        hash_input = json.dumps(key_params, sort_keys=True)
        hash_value = hashlib.md5(hash_input.encode()).hexdigest()[:8]
        return f"{strategy_type}_{hash_value}"
    
    def get_config(self, instance_id: str) -> Optional[InstanceConfig]:
        """获取配置"""
        return self._configs.get(instance_id)
    
    def update_config(self, instance_id: str, updates: Dict[str, Any]) -> bool:
        """动态更新配置"""
        with self._lock:
            config = self._configs.get(instance_id)
            if not config:
                return False
            
            # 分离 API 凭证更新
            api_updates = {}
            param_updates = {}
            
            for key, value in updates.items():
                if key in ['NINE_API_KEY', 'NINE_API_SECRET', 'BINANCE_API_KEY', 'BINANCE_API_SECRET']:
                    if key == 'NINE_API_KEY':
                        api_updates['api_key'] = value
                    elif key == 'NINE_API_SECRET':
                        api_updates['api_secret'] = value
                    elif key == 'BINANCE_API_KEY':
                        api_updates['binance_api_key'] = value
                    elif key == 'BINANCE_API_SECRET':
                        api_updates['binance_api_secret'] = value
                else:
                    param_updates[key] = value
            
            # 验证更新后的配置
            test_params = {**config.strategy_params, **param_updates}
            errors = self._validator.validate(config.strategy_type, test_params)
            if errors:
                raise ValueError(f"配置更新验证失败: {', '.join(errors)}")
            
            # 更新 API 凭证
            if api_updates:
                # 解密现有凭证
                current_credentials = self._encryption.decrypt_credentials(config.api_credentials)
                # 更新并重新加密
                current_credentials.update(api_updates)
                config.api_credentials = self._encryption.encrypt_credentials(current_credentials)
            
            # 更新策略参数
            config.strategy_params.update(param_updates)
            config.updated_at = datetime.now()
            config.version += 1
            
            return True
    
    def get_runtime_config(self, instance_id: str) -> Dict[str, Any]:
        """获取运行时配置（解密后）"""
        config = self._configs.get(instance_id)
        if not config:
            return {}
        
        # 解密 API 凭证
        credentials = self._encryption.decrypt_credentials(config.api_credentials)
        
        # 合并配置
        runtime_config = {
            'NINE_API_KEY': credentials.get('api_key', ''),
            'NINE_API_SECRET': credentials.get('api_secret', ''),
            'BINANCE_API_KEY': credentials.get('binance_api_key', ''),
            'BINANCE_API_SECRET': credentials.get('binance_api_secret', ''),
            **config.strategy_params
        }
        
        return runtime_config
    
    def list_configs(self) -> List[Dict[str, Any]]:
        """列出所有配置（不包含敏感信息）"""
        configs = []
        for config in self._configs.values():
            safe_config = {
                'instance_id': config.instance_id,
                'strategy_type': config.strategy_type,
                'strategy_params': config.strategy_params,
                'created_at': config.created_at.isoformat(),
                'updated_at': config.updated_at.isoformat(),
                'version': config.version,
                'has_credentials': bool(config.api_credentials.get('api_key'))
            }
            configs.append(safe_config)
        return configs
    
    def delete_config(self, instance_id: str) -> bool:
        """删除配置"""
        with self._lock:
            if instance_id in self._configs:
                del self._configs[instance_id]
                return True
            return False
```

### 3.3 实例管理器实现

```python
# app/services/instance_manager.py
import uuid
import threading
import time
import logging
from typing import Dict, Optional, List, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from app.services.config_manager import ConfigManager, InstanceConfig
from app.services.order_management_service import get_oms
from app import setup_trading_bot

class InstanceStatus(Enum):
    """实例状态枚举"""
    CREATED = "created"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    CRASHED = "crashed"

@dataclass
class StrategyInstance:
    """策略实例"""
    instance_id: str
    strategy_type: str
    config: InstanceConfig
    status: InstanceStatus = InstanceStatus.CREATED
    bot_service: Optional[Any] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None
    error_count: int = 0
    last_error: Optional[str] = None
    restart_count: int = 0
    metrics: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'instance_id': self.instance_id,
            'strategy_type': self.strategy_type,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'stopped_at': self.stopped_at.isoformat() if self.stopped_at else None,
            'error_count': self.error_count,
            'last_error': self.last_error,
            'restart_count': self.restart_count,
            'metrics': self.metrics,
            'config': self.config.to_dict()
        }

class InstanceManager:
    """策略实例管理器"""
    
    def __init__(self, app=None):
        self.app = app
        self.instances: Dict[str, StrategyInstance] = {}
        self._lock = threading.RLock()
        self.config_manager = ConfigManager()
        self.oms = get_oms()
        self.logger = logging.getLogger(__name__)
        
        # 健康检查线程
        self._health_check_interval = 30  # 30秒检查一次
        self._health_check_thread = None
        self._health_check_stop_event = threading.Event()
        
        # 自动恢复配置
        self._auto_restart_enabled = True
        self._max_restart_attempts = 3
        
    def start_health_monitor(self):
        """启动健康监控"""
        if self._health_check_thread and self._health_check_thread.is_alive():
            return
            
        self._health_check_stop_event.clear()
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop,
            name="instance-health-monitor",
            daemon=True
        )
        self._health_check_thread.start()
        self.logger.info("实例健康监控已启动")
    
    def stop_health_monitor(self):
        """停止健康监控"""
        if self._health_check_thread:
            self._health_check_stop_event.set()
            self._health_check_thread.join(timeout=5)
            self.logger.info("实例健康监控已停止")
    
    def _health_check_loop(self):
        """健康检查循环"""
        while not self._health_check_stop_event.is_set():
            try:
                self._perform_health_check()
            except Exception as e:
                self.logger.error(f"健康检查异常: {e}")
            
            self._health_check_stop_event.wait(self._health_check_interval)
    
    def _perform_health_check(self):
        """执行健康检查"""
        with self._lock:
            for instance in self.instances.values():
                if instance.status == InstanceStatus.RUNNING:
                    self._check_instance_health(instance)
    
    def _check_instance_health(self, instance: StrategyInstance):
        """检查单个实例健康状态"""
        try:
            if not instance.bot_service:
                return
                
            health_status = instance.bot_service.get_health_status()
            
            # 检查心跳
            heartbeat_age = health_status.get('heartbeat_age', 0)
            if heartbeat_age > 120:  # 2分钟无心跳
                self.logger.warning(f"实例心跳超时: {instance.instance_id}, 年龄: {heartbeat_age}s")
                self._handle_unhealthy_instance(instance, f"心跳超时: {heartbeat_age}s")
                return
            
            # 检查错误率
            error_count = health_status.get('error_count', 0)
            if error_count > 5:
                self.logger.warning(f"实例错误过多: {instance.instance_id}, 错误数: {error_count}")
                self._handle_unhealthy_instance(instance, f"错误过多: {error_count}")
                return
            
            # 更新指标
            instance.metrics.update(health_status.get('metrics', {}))
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {instance.instance_id}, 错误: {e}")
            self._handle_unhealthy_instance(instance, str(e))
    
    def _handle_unhealthy_instance(self, instance: StrategyInstance, reason: str):
        """处理不健康的实例"""
        instance.error_count += 1
        instance.last_error = reason
        
        if self._auto_restart_enabled and instance.restart_count < self._max_restart_attempts:
            self.logger.info(f"自动重启不健康实例: {instance.instance_id}")
            self._restart_instance(instance)
        else:
            self.logger.error(f"实例标记为崩溃状态: {instance.instance_id}")
            instance.status = InstanceStatus.CRASHED
    
    def _restart_instance(self, instance: StrategyInstance):
        """重启实例"""
        try:
            # 停止当前实例
            if instance.bot_service:
                instance.bot_service.stop(timeout=30)
            
            # 等待一段时间
            time.sleep(5)
            
            # 重新创建和启动
            instance.bot_service = self._create_bot_service(instance)
            if instance.bot_service and instance.bot_service.start_background():
                instance.status = InstanceStatus.RUNNING
                instance.restart_count += 1
                instance.started_at = datetime.now()
                self.logger.info(f"实例重启成功: {instance.instance_id}")
            else:
                instance.status = InstanceStatus.ERROR
                self.logger.error(f"实例重启失败: {instance.instance_id}")
                
        except Exception as e:
            instance.status = InstanceStatus.ERROR
            instance.last_error = str(e)
            self.logger.error(f"实例重启异常: {instance.instance_id}, 错误: {e}")
    
    def create_instance(self, strategy_type: str, params: Dict[str, Any]) -> str:
        """创建策略实例"""
        with self._lock:
            try:
                # 创建配置
                config = self.config_manager.create_config(strategy_type, params)
                
                # 创建实例
                instance = StrategyInstance(
                    instance_id=config.instance_id,
                    strategy_type=strategy_type,
                    config=config
                )
                
                # 注册到 OMS
                strategy_id = f"{strategy_type}_{instance.instance_id}"
                self.oms.register_strategy(strategy_id, strategy_type, params)
                
                # 创建 bot service
                bot_service = self._create_bot_service(instance)
                if not bot_service:
                    raise RuntimeError("创建 Bot Service 失败")
                
                instance.bot_service = bot_service
                
                # 保存实例
                self.instances[instance.instance_id] = instance
                
                # 持久化到数据库（如果需要）
                # self._persist_instance(instance)
                
                self.logger.info(f"策略实例创建成功: {instance.instance_id}")
                return instance.instance_id
                
            except Exception as e:
                self.logger.error(f"创建实例失败: {e}")
                raise
    
    def _create_bot_service(self, instance: StrategyInstance):
        """创建 Bot Service"""
        try:
            # 获取运行时配置
            runtime_config = self.config_manager.get_runtime_config(instance.instance_id)
            
            # 创建实例上下文
            from app.services.instance_logger import InstanceLogger
            instance_logger = InstanceLogger(instance.instance_id)
            
            context = InstanceContext(
                instance_id=instance.instance_id,
                strategy_type=instance.strategy_type,
                config=runtime_config,
                logger=instance_logger,
                oms_client=self.oms
            )
            
            # 使用现有的 setup_trading_bot 逻辑，但传入特定配置
            # 需要修改 setup_trading_bot 函数支持传入配置
            bot_service = self._setup_trading_bot_with_config(
                app=self.app,
                strategy_type=instance.strategy_type,
                config=runtime_config,
                context=context
            )
            
            return bot_service
            
        except Exception as e:
            self.logger.error(f"创建 Bot Service 失败: {instance.instance_id}, 错误: {e}")
            return None
    
    def _setup_trading_bot_with_config(self, app, strategy_type: str, config: Dict[str, Any], context: InstanceContext):
        """使用特定配置创建 TradingBotService"""
        # 这里需要基于现有的 setup_trading_bot 函数进行改造
        # 让它支持传入配置而不是从环境变量读取
        
        from app.services.nine_client import NineClient
        from app.services.binance_client import BinanceClient
        
        # 创建 Nine 客户端
        nine_client = NineClient(
            api_url=config.get('NINE_API_URL') or app.config.get('NINE_API_URL'),
            wallet_api_url=config.get('NINE_WALLET_API_URL') or app.config.get('NINE_WALLET_API_URL'),
            api_key=config.get('NINE_API_KEY'),
            secret=config.get('NINE_API_SECRET'),
            logger=context.logger
        )
        
        # 创建 Binance 客户端（如果需要）
        binance_client = None
        if config.get('BINANCE_API_KEY'):
            binance_client = BinanceClient(
                base_url=config.get('BINANCE_API_URL') or app.config.get('BINANCE_API_URL'),
                api_key=config.get('BINANCE_API_KEY'),
                api_secret=config.get('BINANCE_API_SECRET')
            )
        
        # 创建策略实例
        strategy_instance = self._create_strategy_instance(strategy_type, config, nine_client, binance_client, context.logger)
        if not strategy_instance:
            return None
        
        # 创建 TradingBotService
        bot_service = TradingBotService(
            context=context,
            nine_client=nine_client,
            strategy=strategy_instance,
            config=config,
            logger=context.logger,
            binance_client=binance_client
        )
        
        return bot_service
    
    def _create_strategy_instance(self, strategy_type: str, config: Dict[str, Any], nine_client, binance_client, logger):
        """创建策略实例"""
        # 导入策略类
        if strategy_type == "adaptive_kline":
            from app.strategies.adaptive_kline_strategy import AdaptiveKlineStrategy
            return AdaptiveKlineStrategy(config, nine_client, logger)
        elif strategy_type == "mirror_binance":
            from app.strategies.mirror_binance_strategy import MirrorBinanceStrategy
            return MirrorBinanceStrategy(config, nine_client, binance_client, logger)
        # ... 其他策略类型
        else:
            logger.error(f"不支持的策略类型: {strategy_type}")
            return None
    
    def start_instance(self, instance_id: str) -> bool:
        """启动实例"""
        with self._lock:
            instance = self.instances.get(instance_id)
            if not instance:
                self.logger.error(f"实例不存在: {instance_id}")
                return False
                
            if instance.status == InstanceStatus.RUNNING:
                self.logger.warning(f"实例已在运行: {instance_id}")
                return True
            
            try:
                instance.status = InstanceStatus.STARTING
                
                if instance.bot_service and instance.bot_service.start_background():
                    instance.status = InstanceStatus.RUNNING
                    instance.started_at = datetime.now()
                    instance.stopped_at = None
                    self.logger.info(f"实例启动成功: {instance_id}")
                    return True
                else:
                    instance.status = InstanceStatus.ERROR
                    instance.last_error = "Bot Service 启动失败"
                    self.logger.error(f"实例启动失败: {instance_id}")
                    return False
                    
            except Exception as e:
                instance.status = InstanceStatus.ERROR
                instance.last_error = str(e)
                self.logger.error(f"启动实例异常: {instance_id}, 错误: {e}")
                return False
    
    def stop_instance(self, instance_id: str) -> bool:
        """停止实例"""
        with self._lock:
            instance = self.instances.get(instance_id)
            if not instance:
                return False
                
            if instance.status == InstanceStatus.STOPPED:
                return True
            
            try:
                instance.status = InstanceStatus.STOPPING
                
                if instance.bot_service and instance.bot_service.stop():
                    instance.status = InstanceStatus.STOPPED
                    instance.stopped_at = datetime.now()
                    self.logger.info(f"实例停止成功: {instance_id}")
                    return True
                else:
                    instance.status = InstanceStatus.ERROR
                    instance.last_error = "Bot Service 停止失败"
                    self.logger.error(f"实例停止失败: {instance_id}")
                    return False
                    
            except Exception as e:
                instance.status = InstanceStatus.ERROR
                instance.last_error = str(e)
                self.logger.error(f"停止实例异常: {instance_id}, 错误: {e}")
                return False
    
    def pause_instance(self, instance_id: str) -> bool:
        """暂停实例"""
        with self._lock:
            instance = self.instances.get(instance_id)
            if not instance or instance.status != InstanceStatus.RUNNING:
                return False
            
            if instance.bot_service and instance.bot_service.pause():
                instance.status = InstanceStatus.PAUSED
                self.logger.info(f"实例暂停成功: {instance_id}")
                return True
            return False
    
    def resume_instance(self, instance_id: str) -> bool:
        """恢复实例"""
        with self._lock:
            instance = self.instances.get(instance_id)
            if not instance or instance.status != InstanceStatus.PAUSED:
                return False
            
            if instance.bot_service and instance.bot_service.resume():
                instance.status = InstanceStatus.RUNNING
                self.logger.info(f"实例恢复成功: {instance_id}")
                return True
            return False
    
    def delete_instance(self, instance_id: str, force: bool = False) -> bool:
        """删除实例"""
        with self._lock:
            instance = self.instances.get(instance_id)
            if not instance:
                return False
            
            # 如果实例在运行，先停止
            if instance.status in [InstanceStatus.RUNNING, InstanceStatus.PAUSED]:
                if not force:
                    raise ValueError("实例正在运行，请先停止或使用强制删除")
                self.stop_instance(instance_id)
            
            # 清理 OMS 数据
            strategy_id = f"{instance.strategy_type}_{instance.instance_id}"
            self.oms.clear_strategy_active_orders(strategy_id)
            
            # 删除配置
            self.config_manager.delete_config(instance_id)
            
            # 删除实例
            del self.instances[instance_id]
            
            self.logger.info(f"实例删除成功: {instance_id}")
            return True
    
    def get_instance_status(self, instance_id: str) -> Optional[Dict[str, Any]]:
        """获取实例状态"""
        instance = self.instances.get(instance_id)
        if not instance:
            return None
        
        # 获取 OMS 统计
        strategy_id = f"{instance.strategy_type}_{instance.instance_id}"
        oms_stats = self.oms.get_strategy_statistics(strategy_id)
        
        # 获取健康状态
        health_status = {}
        if instance.bot_service:
            try:
                health_status = instance.bot_service.get_health_status()
            except:
                pass
        
        status_dict = instance.to_dict()
        status_dict.update({
            'oms_statistics': oms_stats,
            'health_status': health_status
        })
        
        return status_dict
    
    def list_instances(self) -> List[Dict[str, Any]]:
        """列出所有实例"""
        instances = []
        for instance_id in self.instances:
            status = self.get_instance_status(instance_id)
            if status:
                instances.append(status)
        return instances
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        total_instances = len(self.instances)
        running_instances = len([i for i in self.instances.values() if i.status == InstanceStatus.RUNNING])
        error_instances = len([i for i in self.instances.values() if i.status == InstanceStatus.ERROR])
        
        return {
            'total_instances': total_instances,
            'running_instances': running_instances,
            'error_instances': error_instances,
            'health_monitor_running': self._health_check_thread and self._health_check_thread.is_alive(),
            'system_time': datetime.now().isoformat()
        }

# 全局实例管理器
instance_manager = None

def get_instance_manager(app=None) -> InstanceManager:
    """获取实例管理器单例"""
    global instance_manager
    if instance_manager is None:
        instance_manager = InstanceManager(app)
    return instance_manager
```

### 3.4 实例日志系统

```python
# app/services/instance_logger.py
import logging
import json
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from typing import Dict, Any, Optional
import threading

class StructuredFormatter(logging.Formatter):
    """结构化 JSON 日志格式"""
    
    def format(self, record):
        log_obj = {
            'timestamp': datetime.now().isoformat(),
            'level': record.levelname,
            'instance_id': getattr(record, 'instance_id', 'system'),
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': threading.current_thread().name
        }
        
        # 添加额外字段
        if hasattr(record, 'extra_data'):
            log_obj['extra'] = record.extra_data
            
        # 添加异常信息
        if record.exc_info:
            log_obj['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_obj, ensure_ascii=False)

class InstanceLogger:
    """实例级别的日志器"""
    
    def __init__(self, instance_id: str, log_level: str = 'INFO'):
        self.instance_id = instance_id
        self.logger = logging.getLogger(f"strategy.instance.{instance_id}")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 避免重复添加 handler
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 确保日志目录存在
        log_dir = f"logs/instances"
        os.makedirs(log_dir, exist_ok=True)
        
        # 文件处理器 - 每个实例独立文件
        file_handler = RotatingFileHandler(
            f"{log_dir}/{self.instance_id}.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # 控制台处理器（可选）
        console_handler = logging.StreamHandler()
        
        # 设置格式化器
        formatter = StructuredFormatter()
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        # self.logger.addHandler(console_handler)  # 取消注释以启用控制台输出
        
        # 防止日志传播到父日志器
        self.logger.propagate = False
    
    def _log_with_context(self, level: str, message: str, **kwargs):
        """带上下文的日志记录"""
        extra = {
            'instance_id': self.instance_id,
            'extra_data': kwargs
        }
        getattr(self.logger, level)(message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self._log_with_context('debug', message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self._log_with_context('info', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self._log_with_context('warning', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self._log_with_context('error', message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self._log_with_context('critical', message, **kwargs)
    
    def trade_log(self, action: str, symbol: str, price: float, quantity: float, **kwargs):
        """交易专用日志"""
        trade_data = {
            'action': action,
            'symbol': symbol,
            'price': price,
            'quantity': quantity,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.info(f"交易执行: {action} {quantity} {symbol} @ {price}", **trade_data)
    
    def performance_log(self, metric_name: str, value: float, unit: str = '', **kwargs):
        """性能指标日志"""
        perf_data = {
            'metric': metric_name,
            'value': value,
            'unit': unit,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.info(f"性能指标: {metric_name}={value}{unit}", **perf_data)
```

## 四、API 设计

### 4.1 RESTful API 端点

```python
# app/api/v1/instances.py
from flask import Blueprint, request, jsonify, current_app
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from marshmallow import Schema, fields, ValidationError
from app.services.instance_manager import get_instance_manager
import logging

# 限流器
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["100 per hour", "20 per minute"]
)

api_v1 = Blueprint('api_v1', __name__, url_prefix='/api/v1')
logger = logging.getLogger(__name__)

# 请求验证 Schema
class CreateInstanceSchema(Schema):
    strategy_type = fields.Str(required=True, validate=lambda x: x in [
        'adaptive_kline', 'mirror_binance', 'liquidity_provider', 
        'enhanced_liquidity_provider', 'volume_kline', 'cumulative_depth'
    ])
    config = fields.Dict(required=True)
    auto_start = fields.Bool(missing=False)

class UpdateConfigSchema(Schema):
    config = fields.Dict(required=True)

def require_auth(f):
    """简单的认证装饰器（后续可以扩展）"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 这里可以添加 JWT 或其他认证逻辑
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Missing or invalid authorization header'}), 401
        
        # 简单的 token 验证（生产环境需要更复杂的逻辑）
        token = auth_header.split(' ')[1]
        if token != current_app.config.get('API_TOKEN', 'dev-token'):
            return jsonify({'error': 'Invalid token'}), 401
        
        return f(*args, **kwargs)
    return decorated_function

def validate_json(schema_class):
    """JSON 验证装饰器"""
    def decorator(f):
        from functools import wraps
        
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                schema = schema_class()
                data = schema.load(request.get_json() or {})
                request.validated_data = data
                return f(*args, **kwargs)
            except ValidationError as e:
                return jsonify({'error': 'Validation failed', 'details': e.messages}), 400
        return decorated_function
    return decorator

@api_v1.route('/instances', methods=['POST'])
@require_auth
@validate_json(CreateInstanceSchema)
@limiter.limit("5 per minute")
def create_instance():
    """
    创建策略实例
    POST /api/v1/instances
    {
        "strategy_type": "adaptive_kline",
        "auto_start": true,
        "config": {
            "NINE_API_KEY": "xxx",
            "NINE_API_SECRET": "xxx",
            "AK_TRADING_PAIR": "BTC/USDT",
            "AK_MODE": "bull",
            "AK_ORDER_VALUE": 1.0,
            "AK_BASE_INTERVAL": 60
        }
    }
    """
    try:
        data = request.validated_data
        instance_manager = get_instance_manager(current_app)
        
        instance_id = instance_manager.create_instance(
            strategy_type=data['strategy_type'],
            params=data['config']
        )
        
        # 自动启动（如果需要）
        if data.get('auto_start', False):
            instance_manager.start_instance(instance_id)
        
        logger.info(f"API: 创建实例成功 {instance_id}")
        
        return jsonify({
            'instance_id': instance_id,
            'status': 'created',
            'auto_started': data.get('auto_start', False)
        }), 201
        
    except ValueError as e:
        logger.error(f"API: 创建实例失败 - 验证错误: {e}")
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"API: 创建实例失败 - 系统错误: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances', methods=['GET'])
@require_auth
def list_instances():
    """
    列出所有实例
    GET /api/v1/instances?status=running&strategy_type=adaptive_kline
    """
    try:
        instance_manager = get_instance_manager(current_app)
        instances = instance_manager.list_instances()
        
        # 过滤参数
        status_filter = request.args.get('status')
        strategy_type_filter = request.args.get('strategy_type')
        
        if status_filter:
            instances = [i for i in instances if i['status'] == status_filter]
        if strategy_type_filter:
            instances = [i for i in instances if i['strategy_type'] == strategy_type_filter]
        
        return jsonify({
            'instances': instances,
            'total': len(instances)
        })
        
    except Exception as e:
        logger.error(f"API: 列出实例失败: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>', methods=['GET'])
@require_auth
def get_instance(instance_id):
    """获取实例详情"""
    try:
        instance_manager = get_instance_manager(current_app)
        status = instance_manager.get_instance_status(instance_id)
        
        if not status:
            return jsonify({'error': 'Instance not found'}), 404
            
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"API: 获取实例状态失败 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>/start', methods=['POST'])
@require_auth
@limiter.limit("10 per minute")
def start_instance(instance_id):
    """启动实例"""
    try:
        instance_manager = get_instance_manager(current_app)
        
        if instance_manager.start_instance(instance_id):
            logger.info(f"API: 启动实例成功 {instance_id}")
            return jsonify({'status': 'started', 'instance_id': instance_id})
        else:
            logger.warning(f"API: 启动实例失败 {instance_id}")
            return jsonify({'error': 'Failed to start instance'}), 400
            
    except Exception as e:
        logger.error(f"API: 启动实例异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>/stop', methods=['POST'])
@require_auth
@limiter.limit("10 per minute")
def stop_instance(instance_id):
    """停止实例"""
    try:
        instance_manager = get_instance_manager(current_app)
        
        if instance_manager.stop_instance(instance_id):
            logger.info(f"API: 停止实例成功 {instance_id}")
            return jsonify({'status': 'stopped', 'instance_id': instance_id})
        else:
            logger.warning(f"API: 停止实例失败 {instance_id}")
            return jsonify({'error': 'Failed to stop instance'}), 400
            
    except Exception as e:
        logger.error(f"API: 停止实例异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>/pause', methods=['POST'])
@require_auth
def pause_instance(instance_id):
    """暂停实例"""
    try:
        instance_manager = get_instance_manager(current_app)
        
        if instance_manager.pause_instance(instance_id):
            return jsonify({'status': 'paused', 'instance_id': instance_id})
        else:
            return jsonify({'error': 'Failed to pause instance'}), 400
            
    except Exception as e:
        logger.error(f"API: 暂停实例异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>/resume', methods=['POST'])
@require_auth
def resume_instance(instance_id):
    """恢复实例"""
    try:
        instance_manager = get_instance_manager(current_app)
        
        if instance_manager.resume_instance(instance_id):
            return jsonify({'status': 'running', 'instance_id': instance_id})
        else:
            return jsonify({'error': 'Failed to resume instance'}), 400
            
    except Exception as e:
        logger.error(f"API: 恢复实例异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>/config', methods=['PUT'])
@require_auth
@validate_json(UpdateConfigSchema)
def update_instance_config(instance_id):
    """更新实例配置"""
    try:
        data = request.validated_data
        instance_manager = get_instance_manager(current_app)
        
        if instance_manager.config_manager.update_config(instance_id, data['config']):
            logger.info(f"API: 更新配置成功 {instance_id}")
            return jsonify({'status': 'updated', 'instance_id': instance_id})
        else:
            return jsonify({'error': 'Instance not found or update failed'}), 404
            
    except ValueError as e:
        logger.error(f"API: 更新配置失败 {instance_id} - 验证错误: {e}")
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"API: 更新配置异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>', methods=['DELETE'])
@require_auth
def delete_instance(instance_id):
    """删除实例"""
    try:
        force = request.args.get('force', 'false').lower() == 'true'
        instance_manager = get_instance_manager(current_app)
        
        if instance_manager.delete_instance(instance_id, force=force):
            logger.info(f"API: 删除实例成功 {instance_id}")
            return jsonify({'status': 'deleted', 'instance_id': instance_id})
        else:
            return jsonify({'error': 'Instance not found'}), 404
            
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"API: 删除实例异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/instances/<instance_id>/logs', methods=['GET'])
@require_auth
def get_instance_logs(instance_id):
    """获取实例日志"""
    try:
        lines = int(request.args.get('lines', 100))
        level = request.args.get('level', 'INFO').upper()
        
        # 读取日志文件
        log_file = f"logs/instances/{instance_id}.log"
        if not os.path.exists(log_file):
            return jsonify({'error': 'Log file not found'}), 404
        
        import json
        logs = []
        
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            
            # 获取最后 N 行
            recent_lines = all_lines[-lines:] if lines > 0 else all_lines
            
            for line in recent_lines:
                try:
                    log_entry = json.loads(line.strip())
                    if level == 'ALL' or log_entry.get('level') == level:
                        logs.append(log_entry)
                except json.JSONDecodeError:
                    # 处理非 JSON 格式的行
                    logs.append({
                        'timestamp': '',
                        'level': 'INFO',
                        'message': line.strip(),
                        'raw': True
                    })
        
        return jsonify({
            'logs': logs,
            'total': len(logs),
            'instance_id': instance_id
        })
        
    except Exception as e:
        logger.error(f"API: 获取日志异常 {instance_id}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@api_v1.route('/system/status', methods=['GET'])
@require_auth
def get_system_status():
    """获取系统状态"""
    try:
        instance_manager = get_instance_manager(current_app)
        system_status = instance_manager.get_system_status()
        
        return jsonify(system_status)
        
    except Exception as e:
        logger.error(f"API: 获取系统状态异常: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# 错误处理器
@api_v1.errorhandler(429)
def ratelimit_handler(e):
    """限流错误处理"""
    return jsonify({
        'error': 'Rate limit exceeded',
        'message': str(e.description)
    }), 429

@api_v1.errorhandler(404)
def not_found_handler(e):
    """404 错误处理"""
    return jsonify({
        'error': 'Resource not found',
        'message': 'The requested resource was not found'
    }), 404

@api_v1.errorhandler(500)
def internal_error_handler(e):
    """500 错误处理"""
    return jsonify({
        'error': 'Internal server error',
        'message': 'An unexpected error occurred'
    }), 500
```

## 五、实施计划

### 第一阶段：核心改造（5天）

#### Day 1-2: 策略运行模式改造
**目标**：让现有策略支持后台运行

**任务**：
- [ ] 改造 `TradingBotService` 类
  - 添加 `start_background()`, `stop()`, `pause()`, `resume()` 方法
  - 实现线程管理和状态控制
  - 添加健康检查机制
- [ ] 创建 `InstanceContext` 数据类
- [ ] 修改策略执行循环，支持优雅停止
- [ ] 添加错误处理和自动重试机制

**验收标准**：
- 可以在后台线程启动策略
- 能够优雅停止和暂停/恢复
- 错误不会导致整个程序崩溃

#### Day 3-4: 配置系统改造
**目标**：实现动态配置管理

**任务**：
- [ ] 实现 `ConfigManager` 类
  - 配置创建、更新、删除
  - 配置验证和加密
- [ ] 创建 `ConfigValidator` 类
  - 各策略的验证 Schema
  - 参数类型和范围检查
- [ ] 实现 `ConfigEncryption` 类
  - API 密钥加密存储
  - 安全的密钥管理

**验收标准**：
- 配置验证正常工作
- API 密钥安全加密存储
- 支持运行时配置更新

#### Day 5: 实例管理器
**目标**：统一管理所有策略实例

**任务**：
- [ ] 实现 `InstanceManager` 类
  - 实例创建、启动、停止、删除
  - 健康监控和自动恢复
- [ ] 集成 OMS 和日志系统
- [ ] 实现持久化机制（可选）

**验收标准**：
- 可以创建和管理多个实例
- 健康检查正常工作
- 实例状态准确反映

### 第二阶段：API 和监控（3天）

#### Day 6: API 端点开发
**任务**：
- [ ] 创建 RESTful API 端点
- [ ] 实现请求验证和认证
- [ ] 添加限流和错误处理
- [ ] 编写 API 文档

#### Day 7: 监控和日志
**任务**：
- [ ] 实现 `InstanceLogger` 类
- [ ] 添加性能指标收集
- [ ] 创建日志查看 API
- [ ] 实现结构化日志

#### Day 8: 测试和优化
**任务**：
- [ ] 编写单元测试
- [ ] 进行集成测试
- [ ] 性能优化和调试
- [ ] 修复发现的问题

### 第三阶段：前端开发（3天）

#### Day 9-10: Web UI
**任务**：
- [ ] React/Vue 管理界面开发
- [ ] 实时状态展示
- [ ] 配置表单和验证
- [ ] 日志查看界面

#### Day 11: 集成和部署
**任务**：
- [ ] 前后端集成测试
- [ ] 部署脚本和文档
- [ ] 用户手册编写

## 六、风险和挑战

### 6.1 技术风险

#### 1. 并发问题
**风险**：多线程可能导致竞态条件和数据不一致
**缓解措施**：
- 使用线程锁和原子操作
- 明确的线程边界和数据隔离
- 充分的并发测试

#### 2. 内存泄漏
**风险**：长时间运行可能累积内存，导致系统不稳定
**缓解措施**：
- 定期内存监控和清理
- 使用 weak references 避免循环引用
- 定时重启机制

#### 3. 配置冲突
**风险**：实例间配置互相影响，导致交易错误
**缓解措施**：
- 严格的配置隔离机制
- 配置验证和冲突检测
- 沙盒环境测试

#### 4. 数据库锁竞争
**风险**：SQLite 在高并发下可能出现锁等待
**缓解措施**：
- 使用 WAL 模式提高并发性
- 批量操作减少锁竞争
- 考虑升级到 PostgreSQL

### 6.2 业务风险

#### 1. 交易风险
**风险**：代码缺陷可能导致异常交易
**缓解措施**：
- 严格的测试流程
- 风险控制参数
- 实时监控和熔断机制

#### 2. API 密钥泄漏
**风险**：配置管理不当可能泄漏敏感信息
**缓解措施**：
- 端到端加密存储
- 访问控制和审计日志
- 定期密钥轮换

### 6.3 向后兼容性

**保证措施**：
- 保留原有 CLI 命令接口
- 支持旧配置格式
- 提供迁移工具和文档
- 渐进式升级路径

## 七、测试策略

### 7.1 单元测试

```python
# tests/test_instance_manager.py
import unittest
from unittest.mock import Mock, patch
from app.services.instance_manager import InstanceManager, InstanceStatus

class TestInstanceManager(unittest.TestCase):
    
    def setUp(self):
        self.instance_manager = InstanceManager()
    
    def test_create_instance_success(self):
        """测试成功创建实例"""
        params = {
            'NINE_API_KEY': 'test_key',
            'NINE_API_SECRET': 'test_secret',
            'AK_TRADING_PAIR': 'BTC/USDT',
            'AK_MODE': 'bull',
            'AK_ORDER_VALUE': 1.0
        }
        
        instance_id = self.instance_manager.create_instance('adaptive_kline', params)
        
        self.assertIsNotNone(instance_id)
        self.assertIn(instance_id, self.instance_manager.instances)
        
        instance = self.instance_manager.instances[instance_id]
        self.assertEqual(instance.strategy_type, 'adaptive_kline')
        self.assertEqual(instance.status, InstanceStatus.CREATED)
    
    def test_create_instance_invalid_config(self):
        """测试无效配置创建实例"""
        params = {
            'NINE_API_KEY': 'test_key',
            # 缺少必需参数
        }
        
        with self.assertRaises(ValueError):
            self.instance_manager.create_instance('adaptive_kline', params)
    
    def test_start_instance(self):
        """测试启动实例"""
        # 创建实例
        instance_id = self._create_test_instance()
        
        # 启动实例
        result = self.instance_manager.start_instance(instance_id)
        
        self.assertTrue(result)
        instance = self.instance_manager.instances[instance_id]
        self.assertEqual(instance.status, InstanceStatus.RUNNING)
    
    def test_concurrent_operations(self):
        """测试并发操作"""
        import threading
        import time
        
        results = []
        
        def create_instance(index):
            params = {
                'NINE_API_KEY': f'test_key_{index}',
                'NINE_API_SECRET': f'test_secret_{index}',
                'AK_TRADING_PAIR': 'BTC/USDT',
                'AK_MODE': 'bull',
                'AK_ORDER_VALUE': 1.0
            }
            try:
                instance_id = self.instance_manager.create_instance('adaptive_kline', params)
                results.append(('success', instance_id))
            except Exception as e:
                results.append(('error', str(e)))
        
        # 创建多个线程并发创建实例
        threads = []
        for i in range(10):
            thread = threading.Thread(target=create_instance, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        success_count = len([r for r in results if r[0] == 'success'])
        self.assertEqual(success_count, 10)
    
    def _create_test_instance(self):
        """创建测试实例"""
        params = {
            'NINE_API_KEY': 'test_key',
            'NINE_API_SECRET': 'test_secret',
            'AK_TRADING_PAIR': 'BTC/USDT',
            'AK_MODE': 'bull',
            'AK_ORDER_VALUE': 1.0
        }
        return self.instance_manager.create_instance('adaptive_kline', params)
```

### 7.2 集成测试

```python
# tests/test_api_integration.py
import unittest
import json
from app import create_app
from app.services.instance_manager import get_instance_manager

class TestAPIIntegration(unittest.TestCase):
    
    def setUp(self):
        self.app = create_app({'TESTING': True})
        self.client = self.app.test_client()
        self.headers = {
            'Authorization': 'Bearer dev-token',
            'Content-Type': 'application/json'
        }
    
    def test_create_and_manage_instance(self):
        """测试完整的实例管理流程"""
        
        # 1. 创建实例
        create_data = {
            'strategy_type': 'adaptive_kline',
            'config': {
                'NINE_API_KEY': 'test_key',
                'NINE_API_SECRET': 'test_secret',
                'AK_TRADING_PAIR': 'BTC/USDT',
                'AK_MODE': 'bull',
                'AK_ORDER_VALUE': 1.0
            },
            'auto_start': False
        }
        
        response = self.client.post('/api/v1/instances', 
                                  data=json.dumps(create_data),
                                  headers=self.headers)
        
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        instance_id = data['instance_id']
        
        # 2. 获取实例状态
        response = self.client.get(f'/api/v1/instances/{instance_id}', 
                                 headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['strategy_type'], 'adaptive_kline')
        self.assertEqual(data['status'], 'created')
        
        # 3. 启动实例
        response = self.client.post(f'/api/v1/instances/{instance_id}/start',
                                  headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
        
        # 4. 验证状态变更
        response = self.client.get(f'/api/v1/instances/{instance_id}',
                                 headers=self.headers)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'running')
        
        # 5. 停止实例
        response = self.client.post(f'/api/v1/instances/{instance_id}/stop',
                                  headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
        
        # 6. 删除实例
        response = self.client.delete(f'/api/v1/instances/{instance_id}',
                                    headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
```

### 7.3 压力测试

```python
# tests/test_performance.py
import unittest
import threading
import time
import requests
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

class TestPerformance(unittest.TestCase):
    
    def setUp(self):
        self.base_url = 'http://localhost:5000/api/v1'
        self.headers = {
            'Authorization': 'Bearer dev-token',
            'Content-Type': 'application/json'
        }
    
    def test_concurrent_instance_creation(self):
        """测试并发创建实例"""
        
        def create_instance(index):
            data = {
                'strategy_type': 'adaptive_kline',
                'config': {
                    'NINE_API_KEY': f'test_key_{index}',
                    'NINE_API_SECRET': f'test_secret_{index}',
                    'AK_TRADING_PAIR': 'BTC/USDT',
                    'AK_MODE': 'bull',
                    'AK_ORDER_VALUE': 1.0
                }
            }
            
            start_time = time.time()
            response = requests.post(f'{self.base_url}/instances',
                                   json=data, headers=self.headers)
            duration = time.time() - start_time
            
            return {
                'index': index,
                'status_code': response.status_code,
                'duration': duration,
                'response': response.json() if response.status_code == 201 else None
            }
        
        # 并发创建 50 个实例
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_instance, i) for i in range(50)]
            results = [future.result() for future in as_completed(futures)]
        
        # 分析结果
        successful = [r for r in results if r['status_code'] == 201]
        failed = [r for r in results if r['status_code'] != 201]
        
        avg_duration = sum(r['duration'] for r in successful) / len(successful)
        max_duration = max(r['duration'] for r in successful)
        
        print(f"成功: {len(successful)}, 失败: {len(failed)}")
        print(f"平均响应时间: {avg_duration:.2f}s, 最大响应时间: {max_duration:.2f}s")
        
        # 验证
        self.assertGreaterEqual(len(successful), 45)  # 90% 成功率
        self.assertLess(avg_duration, 1.0)  # 平均响应时间 < 1秒
    
    def test_api_rate_limiting(self):
        """测试 API 限流"""
        
        def make_request():
            response = requests.get(f'{self.base_url}/instances',
                                  headers=self.headers)
            return response.status_code
        
        # 快速发送 30 个请求（超过限制）
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(30)]
            status_codes = [future.result() for future in as_completed(futures)]
        
        # 应该有一些请求被限流
        rate_limited = len([code for code in status_codes if code == 429])
        self.assertGreater(rate_limited, 0)
```

## 八、验收标准

### 8.1 功能验收

#### 基础功能
- [ ] 可同时运行 20+ 策略实例
- [ ] API 所有端点正常工作
- [ ] 配置热更新生效
- [ ] 实例状态准确反映

#### 高级功能
- [ ] 健康检查和自动恢复
- [ ] 日志隔离和查看
- [ ] 性能监控数据收集
- [ ] 优雅停止和重启

### 8.2 性能验收

#### 响应时间
- [ ] API 响应时间 < 200ms (P95)
- [ ] 实例创建时间 < 5秒
- [ ] 状态查询 < 100ms

#### 并发性能
- [ ] 支持 100+ 并发实例运行
- [ ] 10 QPS API 请求处理
- [ ] 内存使用稳定（无泄漏）

#### 系统资源
- [ ] CPU 使用率 < 80%
- [ ] 内存增长 < 5% /小时
- [ ] 磁盘 I/O 正常

### 8.3 质量验收

#### 代码质量
- [ ] 测试覆盖率 > 80%
- [ ] 无严重安全漏洞
- [ ] 代码审查通过
- [ ] 文档完整

#### 稳定性
- [ ] 7x24 小时运行无崩溃
- [ ] 错误恢复机制正常
- [ ] 日志记录完整
- [ ] 监控告警正常

## 九、后续优化计划

### 9.1 短期优化（1-2 周）

1. **性能优化**
   - 数据库查询优化
   - 缓存机制实现
   - 异步处理优化

2. **功能增强**
   - 批量操作支持
   - 配置模板系统
   - 定时任务支持

3. **监控完善**
   - 更多性能指标
   - 告警规则配置
   - 图表可视化

### 9.2 中期扩展（1-2 月）

1. **容器化部署**
   - Docker 镜像构建
   - docker-compose 编排
   - 健康检查配置

2. **分布式支持**
   - 消息队列集成
   - 分布式锁机制
   - 负载均衡

3. **高级功能**
   - 策略回测系统
   - 参数优化建议
   - 风险控制增强

### 9.3 长期规划（3-6 月）

1. **Kubernetes 编排**
   - Helm Chart 开发
   - 自动扩缩容
   - 滚动更新

2. **多租户架构**
   - 团队协作功能
   - 权限管理系统
   - 资源隔离

3. **AI 增强**
   - 参数自动调优
   - 市场预测辅助
   - 智能风控建议

## 十、结论

本改造计划将分阶段实施，确保系统稳定性的同时逐步增强功能。重点关注：

1. **稳定性优先**：确保现有功能不受影响
2. **渐进式改造**：分阶段实施，降低风险
3. **质量保证**：充分测试，文档齐全
4. **可扩展设计**：为未来功能扩展预留空间

通过这个改造，系统将从单实例应用升级为支持大规模并发的策略管理平台，为后续的业务扩展奠定坚实基础。