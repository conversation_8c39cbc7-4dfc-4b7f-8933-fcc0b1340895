#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
当前挂单数量统计脚本
=================

功能：
- 查询指定交易对的当前活跃挂单
- 统计挂单总数量
- 显示订单详细信息

使用方法：
python check_orders.py [交易对名称]

例如：
python check_orders.py SEPBTC/USDT
python check_orders.py NINE/USDT
"""

import sys
import os
import logging
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.nine_client import NineClient


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def load_env_config():
    """加载环境变量配置"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        # 如果没有python-dotenv，尝试手动读取.env文件
        env_file = os.path.join(os.path.dirname(__file__), '.env')
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value

    # 首先尝试通用API配置
    api_key = os.getenv('NINE_API_KEY')
    api_secret = os.getenv('NINE_API_SECRET')
    
    if api_key and api_secret:
        return {
            'api_key': api_key,
            'api_secret': api_secret,
            'api_url': os.getenv('NINE_API_URL', 'https://appdev.binineex.com')
        }
    
    # 获取策略特定的API配置
    api_configs = [
        ('MM_NINE_API_KEY', 'MM_NINE_API_SECRET'),  # MirrorBinance策略配置
        ('VK_NINE_API_KEY', 'VK_NINE_API_SECRET'),   # VolumeKline策略配置
        ('CDS_NINE_API_KEY', 'CDS_NINE_API_SECRET'), # CumulativeDepth策略配置
        ('LP_NINE_API_KEY', 'LP_NINE_API_SECRET'),   # LiquidityProvider策略配置
        ('ELP_NINE_API_KEY', 'ELP_NINE_API_SECRET'), # EnhancedLP策略配置
    ]
    
    # 尝试获取可用的API凭证
    for api_key_env, api_secret_env in api_configs:
        api_key = os.getenv(api_key_env)
        api_secret = os.getenv(api_secret_env)
        if api_key and api_secret:
            return {
                'api_key': api_key,
                'api_secret': api_secret,
                'api_url': os.getenv('NINE_API_URL', 'https://appdev.binineex.com')
            }
    
    raise ValueError("无法找到有效的Nine CEX API凭证。请检查环境变量配置。")


def format_order_info(order: Dict[str, Any]) -> str:
    """格式化订单信息"""
    order_id = order.get('orderId', 'N/A')
    direction = '买入' if order.get('orderDirection') == 1 else '卖出'
    price = order.get('orderPrice', 0)
    quantity = order.get('orderQuantity', 0)
    filled_qty = order.get('filledQuantity', 0)
    status_map = {
        0: '待成交',
        1: '部分成交',
        2: '完全成交',
        3: '已撤销',
        4: '失败'
    }
    status = status_map.get(order.get('orderStatus', 0), '未知')
    
    return f"  订单ID: {order_id} | {direction} | 价格: {price} | 数量: {quantity} | 已成交: {filled_qty} | 状态: {status}"


def check_orders(trade_pair: str, config: Dict[str, str], logger: logging.Logger) -> Optional[Dict[str, Any]]:
    """查询指定交易对的当前订单"""
    
    logger.info(f"🔍 开始查询交易对 {trade_pair} 的当前挂单...")
    
    try:
        # 创建Nine CEX客户端
        client = NineClient(
            api_key=config['api_key'],
            secret=config['api_secret'],
            api_url=config['api_url'],
            logger=logger
        )
        
        # 查询当前订单 - 获取更多数据来分析
        result = client.get_current_orders(
            trade_pair=trade_pair,
            user_api_key=config['api_key'],
            user_api_secret=config['api_secret'],
            page_size=1000,  # 获取更多订单来分析问题
            page_num=1
        )
        
        if result and result.get('code') == 200:
            order_data = result.get('data', {})
            order_list = order_data.get('list', [])
            total_count = order_data.get('total', 0)
            
            logger.info(f"✅ 查询成功！")
            
            # 统计分析
            buy_orders = [order for order in order_list if order.get('orderDirection') == 1]
            sell_orders = [order for order in order_list if order.get('orderDirection') == 2]
            
            print(f"\n{'='*50}")
            print(f"交易对: {trade_pair}")
            print(f"{'='*50}")
            print(f"📊 订单统计:")
            print(f"  总订单数: {total_count}")
            print(f"  当前页订单数: {len(order_list)}")
            print(f"  买单数量: {len(buy_orders)}")
            print(f"  卖单数量: {len(sell_orders)}")
            
            if order_list:
                print(f"\n📋 订单详情:")
                for order in order_list[:20]:  # 只显示前20个订单，避免输出过多
                    print(format_order_info(order))
            
            # 如果总数超过当前页数量，提示可能有更多订单
            if total_count > len(order_list):
                print(f"\n⚠️  注意: 总共有 {total_count} 个订单，当前仅显示前 {len(order_list)} 个")
                print("   如需查看全部订单，请修改脚本中的 page_size 参数")
            
            print(f"{'='*50}")
            
            return result
            
        else:
            logger.error(f"❌ 查询失败: {result}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 查询过程中出现异常: {e}")
        return None


def main():
    """主函数"""
    logger = setup_logging()
    
    # 获取命令行参数
    if len(sys.argv) > 1:
        trade_pair = sys.argv[1]
    else:
        # 默认交易对
        trade_pair = input("请输入要查询的交易对名称（例如: SEPBTC/USDT 或 NINE/USDT）: ").strip()
        if not trade_pair:
            trade_pair = "NINE/USDT"  # 默认值
    
    try:
        # 加载配置
        config = load_env_config()
        logger.info(f"🔑 使用API Key: {config['api_key'][:8]}...")
        logger.info(f"🌐 API URL: {config['api_url']}")
        
        # 查询订单
        result = check_orders(trade_pair, config, logger)
        
        if result:
            logger.info("🎉 订单查询完成！")
        else:
            logger.error("😞 订单查询失败，请检查配置和网络连接")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ 脚本执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()