# =============================================================================
# Raydium价格平衡策略配置 (RaydiumPriceBalanceStrategy)  
# =============================================================================
# 目标：使用链上DEX价格作为参考，在Nine CEX上进行价格平衡
# 数据源：Raydium DEX、Jupiter聚合器等去中心化交易所
# 注意：API密钥从根目录 .env 文件中统一读取

# --- 交易对配置 ---
# Nine CEX交易对
RPB_TRADING_PAIR=USELESS/USDT
# Nine CEX交易对ID (备用)
RPB_TRADING_PAIR_ID=20250620000002
# Solana代币合约地址（优先使用，如果为空则使用代币符号）
RPB_CONTRACT_ADDRESS=Dz9mQ9NzkBcCsuGPFJ3r1bS4wgqKMHBPiVuniW8Mbonk

# --- 💡 核心策略参数 ---
# DEX目标价格偏移（0=与参考CEX价格一致）
RPB_TARGET_PRICE_OFFSET=0
# 价格容差(0.2%，目标价格±0.2%可接受)
RPB_PRICE_TOLERANCE=0.002
# 激进阈值(1%，偏差>1%激进调整)
RPB_AGGRESSIVE_THRESHOLD=0.01

# --- 交易配置 (适配500U资金规模) ---
# 基础订单金额(USDT)
RPB_BASE_ORDER_AMOUNT=0.2
# 最大单笔订单金额(USDT)
RPB_MAX_ORDER_AMOUNT=1
# 最小订单金额(USDT)
RPB_MIN_ORDER_AMOUNT=0.1

# --- 流动性配置 ---
# 流动性层数
RPB_LIQUIDITY_LEVELS=5
# 每层流动性金额(USDT)
RPB_LIQUIDITY_AMOUNT_PER_LEVEL=1.0
# 多层价差: 0.1%, 0.2%, 0.5%, 1.0%, 2.0%
RPB_INITIAL_SPREADS=0.001,0.002,0.005,0.01,0.02
# 各层级倍数
RPB_LEVEL_MULTIPLIERS=1.0,1.2,1.5,2.0,3.0
# 推动步长因子
RPB_PUMP_STEP_FACTORS=1.5,2.0,2.3,2.6,2.9

# --- 推动交易配置 ---
# 小额推动金额(USDT)
RPB_SMALL_TRADE_AMOUNT=1
# 中等推动金额(USDT)
RPB_MEDIUM_TRADE_AMOUNT=5
# 最大步长倍数
RPB_MAX_STEP_MULTIPLIER=8.0
# 最大推动步数
RPB_MAX_PUSH_STEPS=10

# --- 精度配置 ---
# 价格精度(小数点后位数)
RPB_PRICE_PRECISION=8
# 数量精度(小数点后位数) 
RPB_QTY_PRECISION=2

# --- 系统配置 ---
# 策略执行间隔(秒)
RPB_UPDATE_INTERVAL=30
# 990%价格限制(Nine系统限制)
RPB_PRICE_LIMIT_FACTOR=2.9
# 渐进式推动阈值
RPB_PROGRESSIVE_PUMP_THRESHOLD=0.5

# --- 订单管理配置 ---
# 触发清理的最大订单数
RPB_MAX_ORDERS=30
# 清理时保留的最新订单数
RPB_KEEP_ORDERS=20

# --- 常见配置说明 ---
# RPB_CONTRACT_ADDRESS: 如果设置了合约地址，将优先使用合约地址获取链上价格
#                      如果为空或不设置，则使用交易对的base代币符号获取价格
# 例如：
# RPB_TRADING_PAIR=BTC/USDT     # 主流币种，可不设置合约地址
# RPB_TRADING_PAIR=PEPE/USDT    # 代币，建议设置合约地址获得更准确价格
# RPB_CONTRACT_ADDRESS=******************************************  # PEPE合约地址

# 启动：flask start-trading-bot --strategy raydium_price_balance
# 注意：需要Solana RPC节点访问权限或使用公共节点