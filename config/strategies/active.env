# ===== 简单活跃策略配置 =====
# 专注于制造盘口活跃度，遵循KISS原则
# 注意：Nine CEX API 配置从主目录 .env 文件读取

# --- 基础配置 ---
ACTIVE_TRADING_PAIR=ETH/USDT                    # 交易对
ACTIVE_UPDATE_INTERVAL=10                       # 策略执行间隔(秒)

# --- 活跃度配置 ---
ACTIVE_CANCEL_RATIO=0.3                         # 每次随机撤销的订单比例(30%)
ACTIVE_PLACE_COUNT=3                            # 每次随机新增的订单数量
ACTIVE_PRICE_RANGE=0.02                         # 价格随机范围(±2%)
ACTIVE_ORDER_AMOUNT=1.0                         # 单个订单金额(USDT)

# --- OMS订单生命周期管理 ---
ACTIVE_ORDER_MAX_AGE=30                         # 订单最大存活时间(秒)
ACTIVE_MAX_ORDERS=20                            # 最大订单数量限制

# --- 精度配置 ---
ACTIVE_PRICE_PRECISION=8                        # 价格精度
ACTIVE_QTY_PRECISION=6                          # 数量精度


