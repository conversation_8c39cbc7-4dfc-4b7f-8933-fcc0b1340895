# =============================================================================
# CEX价格平衡策略配置 (CexPriceBalanceStrategy)
# =============================================================================
# 目标：使用主流CEX价格作为参考，在Nine CEX上进行价格平衡
# 数据源：Binance、OKX、Bybit等中心化交易所
# 注意：API密钥从根目录 .env 文件中统一读取

# --- 交易对配置 ---
CPB_TRADING_PAIR=DOGE/USDT                # CEX交易对
CPB_TRADING_PAIR_ID=20250826000008        # CEX交易对ID (备用)

# --- CEX数据源配置 ---
CPB_EXCHANGES=binance,okx,bybit               # 使用的交易所列表
CPB_PRIMARY_EXCHANGE=binance                   # 主要交易所
CPB_PRICE_AGGREGATION=median                   # 聚合方式：median/average/max/min

# --- 符号映射配置（如果CEX符号不同）---
# 格式：NINE符号:CEX符号，多个用逗号分隔
# CPB_SYMBOL_MAPPING=SEPBTC:SEP/BTC,SEPUSDT:SEP/USDT

# --- 💡 核心策略参数 ---
CPB_TARGET_PRICE_OFFSET=0.01                # CEX目标价格偏移（0=与参考CEX价格一致）
CPB_PRICE_TOLERANCE=0.002                 # 价格容差(0.2%，目标价格±0.2%可接受)
CPB_AGGRESSIVE_THRESHOLD=0.005             # 激进阈值(0.5%，偏差>0.5%激进调整)

# --- 交易配置 (适配500U资金规模) ---
CPB_BASE_ORDER_AMOUNT=0.2                   # 基础订单金额(USDT)
CPB_MAX_ORDER_AMOUNT=1                   # 最大单笔订单金额(USDT)
CPB_MIN_ORDER_AMOUNT=0.1                  # 最小订单金额(USDT)

# --- 流动性配置 ---
CPB_LIQUIDITY_LEVELS=5                    # 流动性层数
CPB_LIQUIDITY_AMOUNT_PER_LEVEL=5.0          # 每层流动性金额(USDT) - 增加到5 USDT确保有足够数量
CPB_INITIAL_SPREADS=0.001,0.002,0.005,0.01,0.02  # 多层价差: 0.1%, 0.2%, 0.5%, 1.0%, 2.0%
CPB_LEVEL_MULTIPLIERS=1.0,1.2,1.5,2.0,3.0  # 各层级倍数
CPB_PUMP_STEP_FACTORS=1.5,2.0,2.3,2.6,2.9  # 推动步长因子
CPB_TOP_SPREAD=0.001                        # 顶部买单价差(默认0.1%)

# --- 推动交易配置 ---
CPB_SMALL_TRADE_AMOUNT=1                  # 小额推动金额(USDT)
CPB_MEDIUM_TRADE_AMOUNT=3                 # 中等推动金额(USDT)
CPB_MAX_STEP_MULTIPLIER=8.0              # 最大步长倍数
CPB_MAX_PUSH_STEPS=10                    # 最大推动步数

# --- 精度配置 ---
CPB_PRICE_PRECISION=8                     # 价格精度(小数点后位数)
CPB_QTY_PRECISION=2                       # 数量精度(小数点后位数) - 增加到4位确保小数量不被截断

# --- 系统配置 ---
CPB_UPDATE_INTERVAL=20                    # 策略执行间隔(秒)
CPB_PRICE_LIMIT_FACTOR=9.9                # 990%价格限制(Nine系统限制)
CPB_PROGRESSIVE_PUMP_THRESHOLD=0.5        # 渐进式推动阈值

# --- 订单管理配置 ---
CPB_MAX_ORDERS=30                         # 触发清理的最大订单数
CPB_KEEP_ORDERS=20                        # 清理时保留的最新订单数

# --- 常见交易对配置示例 ---
# 对于主流币种，通常可直接使用符号名称
# 对于小币种，可能需要配置符号映射
# 例如：
# CPB_TRADING_PAIR=BTC/USDT    # 主流币种
# CPB_TRADING_PAIR=DOGE/USDT   # 主流币种
# CPB_TRADING_PAIR=PEPE/USDT   # 小币种（需要确认各交易所是否支持）

# 启动：flask start-trading-bot --strategy cex_price_balance
# 注意：确保配置的交易对在选择的CEX上都有交易