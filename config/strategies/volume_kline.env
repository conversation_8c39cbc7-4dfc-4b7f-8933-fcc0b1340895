# =============================================================================
# 成交量K线策略配置 (VolumeKlineStrategy)
# =============================================================================
# 功能：定时定量刷量，增加交易对成交量
# 适用：基于Nine CEX自身订单簿进行交易
# 注意：通用Nine CEX参数（NINE_*）从根目录 .env 文件中统一读取

# --- API配置 ---
# VK_NINE_API_KEY=your_nine_api_key_here
# VK_NINE_API_SECRET=your_nine_api_secret_here
# 注意：优先使用根目录 .env 文件中的通用 NINE_API_KEY 和 NINE_API_SECRET
# 只有在需要使用不同API凭证时才取消注释上面的配置

# --- 基础配置 ---
VK_TRADING_PAIR=NINE/USDT                # 交易对
VK_INTERVAL_SECONDS=60                   # 策略执行间隔 (秒)

# --- 交易配置 ---
VK_ORDER_AMOUNT=0.01                     # 每次下单的基础币种数量
VK_MIN_TRADE_QTY_ASSET=0.01              # 最小允许的交易数量

# --- 精度配置 ---
VK_PRICE_PRECISION=2                     # 价格精度
VK_QTY_PRECISION=2                       # 数量精度

# --- Nine CEX 订单簿配置 ---
VK_NINE_POOL_PRECISION_API_PARAM=0.01   # Nine CEX 订单簿精度参数

# --- 通用 Nine CEX 订单参数 (从根目录 .env 文件读取) ---
# NINE_ACCOUNT_TYPE=1                    # Nine CEX 账户类型
# NINE_ORDER_TYPE_LIMIT=1               # Nine CEX 限价单类型
# NINE_ORDER_DIR_BUY=1                  # Nine CEX 买单方向
# NINE_ORDER_DIR_SELL=2                 # Nine CEX 卖单方向

# --- 功能开关 ---
VK_ENABLE_PERIODIC_TRADE_CHECK=true      # 启用定期成交检测

# 启动：flask start-trading-bot --strategy volume_kline